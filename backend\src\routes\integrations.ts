import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { PrismaClient } from '@prisma/client';
import { verifyAuth } from '../middleware/auth';

const prisma = new PrismaClient();

interface AuthenticatedRequest extends FastifyRequest {
  user?: {
    id: string;
    clerkId: string;
    email: string;
    name: string;
    tenantId: string;
  };
}

interface IntegrationParams {
  id: string;
}

interface CreateIntegrationBody {
  name: string;
  type: string;
  config: Record<string, any>;
  enabled?: boolean;
}

interface UpdateIntegrationBody {
  name?: string;
  config?: Record<string, any>;
  enabled?: boolean;
}

export default async function (fastify: FastifyInstance) {
  // Authentication middleware
  fastify.addHook('onRequest', verifyAuth);

  // Get all integrations for the tenant
  fastify.get('/', async (request: AuthenticatedRequest, reply: FastifyReply) => {
    if (!request.user) {
      return reply.status(401).send({ error: 'Unauthorized' });
    }

    const { tenantId } = request.user;

    try {
      const integrations = await prisma.integration.findMany({
        where: { tenantId },
        orderBy: { createdAt: 'desc' },
      });

      return reply.send(integrations);
    } catch (error) {
      console.error('Error fetching integrations:', error);
      return reply.status(500).send({ error: 'Failed to fetch integrations' });
    }
  });

  // Get a single integration by ID
  fastify.get('/:id', async (request: AuthenticatedRequest<{ Params: IntegrationParams }>, reply: FastifyReply) => {
    if (!request.user) {
      return reply.status(401).send({ error: 'Unauthorized' });
    }

    const { id } = request.params;
    const { tenantId } = request.user;

    try {
      const integration = await prisma.integration.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!integration) {
        return reply.status(404).send({ error: 'Integration not found' });
      }

      return reply.send(integration);
    } catch (error) {
      console.error('Error fetching integration:', error);
      return reply.status(500).send({ error: 'Failed to fetch integration' });
    }
  });

  // Create a new integration
  fastify.post('/', async (request: AuthenticatedRequest<{ Body: CreateIntegrationBody }>, reply: FastifyReply) => {
    if (!request.user) {
      return reply.status(401).send({ error: 'Unauthorized' });
    }

    const { name, type, config, enabled = true } = request.body;
    const { tenantId } = request.user;

    try {
      const integration = await prisma.integration.create({
        data: {
          name,
          type,
          config,
          enabled,
          tenantId,
        },
      });

      return reply.status(201).send(integration);
    } catch (error) {
      console.error('Error creating integration:', error);
      return reply.status(500).send({ error: 'Failed to create integration' });
    }
  });

  // Update an integration
  fastify.patch('/:id', async (request: AuthenticatedRequest<{ Params: IntegrationParams; Body: UpdateIntegrationBody }>, reply: FastifyReply) => {
    if (!request.user) {
      return reply.status(401).send({ error: 'Unauthorized' });
    }

    const { id } = request.params;
    const { name, config, enabled } = request.body;
    const { tenantId } = request.user;

    try {
      // Check if the integration exists and belongs to the tenant
      const existingIntegration = await prisma.integration.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!existingIntegration) {
        return reply.status(404).send({ error: 'Integration not found' });
      }

      // Update the integration
      const integration = await prisma.integration.update({
        where: { id },
        data: {
          name,
          config,
          enabled,
        },
      });

      return reply.send(integration);
    } catch (error) {
      console.error('Error updating integration:', error);
      return reply.status(500).send({ error: 'Failed to update integration' });
    }
  });

  // Delete an integration
  fastify.delete('/:id', async (request: AuthenticatedRequest<{ Params: IntegrationParams }>, reply: FastifyReply) => {
    if (!request.user) {
      return reply.status(401).send({ error: 'Unauthorized' });
    }

    const { id } = request.params;
    const { tenantId } = request.user;

    try {
      // Check if the integration exists and belongs to the tenant
      const existingIntegration = await prisma.integration.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!existingIntegration) {
        return reply.status(404).send({ error: 'Integration not found' });
      }

      // Delete the integration
      await prisma.integration.delete({
        where: { id },
      });

      return reply.status(204).send();
    } catch (error) {
      console.error('Error deleting integration:', error);
      return reply.status(500).send({ error: 'Failed to delete integration' });
    }
  });

  // Test an integration
  fastify.post('/:id/test', async (request: AuthenticatedRequest<{ Params: IntegrationParams }>, reply: FastifyReply) => {
    if (!request.user) {
      return reply.status(401).send({ error: 'Unauthorized' });
    }

    const { id } = request.params;
    const { tenantId } = request.user;

    try {
      // Check if the integration exists and belongs to the tenant
      const integration = await prisma.integration.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!integration) {
        return reply.status(404).send({ error: 'Integration not found' });
      }

      // Test the integration based on its type
      let result;
      switch (integration.type) {
        case 'slack':
          result = await testSlackIntegration(integration.config);
          break;
        case 'zendesk':
          result = await testZendeskIntegration(integration.config);
          break;
        case 'hubspot':
          result = await testHubSpotIntegration(integration.config);
          break;
        default:
          return reply.status(400).send({ error: `Unknown integration type: ${integration.type}` });
      }

      return reply.send(result);
    } catch (error) {
      console.error('Error testing integration:', error);
      return reply.status(500).send({ error: 'Failed to test integration' });
    }
  });
}

/**
 * Test a Slack integration
 */
async function testSlackIntegration(config: any): Promise<{ success: boolean; message: string }> {
  try {
    const { webhookUrl } = config;
    
    if (!webhookUrl) {
      return { success: false, message: 'Webhook URL is required' };
    }
    
    // Import the Slack integration
    const slack = await import('../integrations/slack');
    
    // Send a test message
    const result = await slack.default.sendSlackWebhook(webhookUrl, {
      text: 'This is a test message from EchoPilot',
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '*EchoPilot Test Message*\nThis is a test message to verify your Slack integration is working correctly.',
          },
        },
      ],
    });
    
    return {
      success: result.success,
      message: result.success
        ? 'Test message sent successfully'
        : `Failed to send test message: Status ${result.statusCode}`,
    };
  } catch (error) {
    console.error('Error testing Slack integration:', error);
    return {
      success: false,
      message: `Error testing Slack integration: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Test a Zendesk integration
 */
async function testZendeskIntegration(config: any): Promise<{ success: boolean; message: string }> {
  try {
    const { subdomain, email, apiToken } = config;
    
    if (!subdomain || !email || !apiToken) {
      return { success: false, message: 'Subdomain, email, and API token are required' };
    }
    
    // Import the Zendesk integration
    const zendesk = await import('../integrations/zendesk');
    
    // Create a test ticket
    const result = await zendesk.default.createZendeskTicket(subdomain, email, apiToken, {
      subject: 'EchoPilot Test Ticket',
      comment: {
        body: 'This is a test ticket from EchoPilot to verify your Zendesk integration is working correctly.',
        public: false,
      },
      tags: ['echopilot', 'test'],
    });
    
    return {
      success: result.success,
      message: result.success
        ? `Test ticket created successfully with ID: ${result.ticketId}`
        : `Failed to create test ticket: ${result.error}`,
    };
  } catch (error) {
    console.error('Error testing Zendesk integration:', error);
    return {
      success: false,
      message: `Error testing Zendesk integration: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Test a HubSpot integration
 */
async function testHubSpotIntegration(config: any): Promise<{ success: boolean; message: string }> {
  try {
    const { apiKey, contactId } = config;
    
    if (!apiKey) {
      return { success: false, message: 'API key is required' };
    }
    
    // Import the HubSpot integration
    const hubspot = await import('../integrations/hubspot');
    
    // Create a test note
    const result = await hubspot.default.createHubSpotNote(apiKey, {
      properties: {
        hs_note_body: 'This is a test note from EchoPilot to verify your HubSpot integration is working correctly.',
        hs_timestamp: new Date().getTime().toString(),
      },
      ...(contactId ? {
        associations: [
          {
            to: {
              id: contactId,
            },
            types: [
              {
                associationCategory: 'HUBSPOT_DEFINED',
                associationTypeId: 202, // Contact to note association
              },
            ],
          },
        ],
      } : {}),
    });
    
    return {
      success: result.success,
      message: result.success
        ? `Test note created successfully with ID: ${result.noteId}`
        : `Failed to create test note: ${result.error}`,
    };
  } catch (error) {
    console.error('Error testing HubSpot integration:', error);
    return {
      success: false,
      message: `Error testing HubSpot integration: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}
