'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Header } from '@/components/Header';
import Link from 'next/link';
import { formatDate } from '@/lib/utils';

interface IntegrationData {
  id: string;
  name: string;
  type: string;
  config: Record<string, any>;
  enabled: boolean;
  createdAt: string;
  updatedAt: string;
}

interface PageProps {
  params: {
    id: string;
  };
}

export default function IntegrationPage({ params }: PageProps) {
  const { id } = params;
  const router = useRouter();
  
  const [integration, setIntegration] = useState<IntegrationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [formState, setFormState] = useState<Partial<IntegrationData>>({});
  const [saveLoading, setSaveLoading] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [deleteConfirm, setDeleteConfirm] = useState(false);
  
  useEffect(() => {
    async function fetchIntegration() {
      try {
        const response = await fetch(`/api/integrations/${id}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch integration');
        }
        
        const data = await response.json();
        setIntegration(data);
        setFormState({
          name: data.name,
          config: data.config,
          enabled: data.enabled,
        });
      } catch (error) {
        console.error('Error fetching integration:', error);
        setError('Failed to load integration');
      } finally {
        setLoading(false);
      }
    }
    
    fetchIntegration();
  }, [id]);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name.includes('.')) {
      // Handle nested properties (config.*)
      const [parent, child] = name.split('.');
      setFormState(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof typeof prev],
          [child]: value,
        },
      }));
    } else {
      setFormState(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };
  
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormState(prev => ({
      ...prev,
      [name]: checked,
    }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaveLoading(true);
    setSaveError(null);
    
    try {
      const response = await fetch(`/api/integrations/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formState),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update integration');
      }
      
      const data = await response.json();
      setIntegration(data);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating integration:', error);
      setSaveError(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setSaveLoading(false);
    }
  };
  
  const handleTest = async () => {
    setSaveLoading(true);
    setTestResult(null);
    setSaveError(null);
    
    try {
      const response = await fetch(`/api/integrations/${id}/test`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to test integration');
      }
      
      const result = await response.json();
      setTestResult(result);
    } catch (error) {
      console.error('Error testing integration:', error);
      setSaveError(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setSaveLoading(false);
    }
  };
  
  const handleDelete = async () => {
    if (!deleteConfirm) {
      setDeleteConfirm(true);
      return;
    }
    
    setSaveLoading(true);
    
    try {
      const response = await fetch(`/api/integrations/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete integration');
      }
      
      router.push('/integrations');
    } catch (error) {
      console.error('Error deleting integration:', error);
      setSaveError(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setSaveLoading(false);
    }
  };
  
  const renderFormFields = () => {
    if (!integration) return null;
    
    switch (integration.type) {
      case 'slack':
        return (
          <div className="space-y-4">
            <div>
              <label htmlFor="config.webhookUrl" className="block text-sm font-medium mb-1">
                Webhook URL <span className="text-error">*</span>
              </label>
              <input
                type="text"
                id="config.webhookUrl"
                name="config.webhookUrl"
                value={formState.config?.webhookUrl || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-foreground/10 rounded-lg bg-foreground/5"
                placeholder="https://hooks.slack.com/services/..."
                required
                disabled={!isEditing}
              />
            </div>
          </div>
        );
      
      case 'zendesk':
        return (
          <div className="space-y-4">
            <div>
              <label htmlFor="config.subdomain" className="block text-sm font-medium mb-1">
                Subdomain <span className="text-error">*</span>
              </label>
              <div className="flex items-center">
                <input
                  type="text"
                  id="config.subdomain"
                  name="config.subdomain"
                  value={formState.config?.subdomain || ''}
                  onChange={handleInputChange}
                  className="flex-1 px-3 py-2 border border-foreground/10 rounded-lg bg-foreground/5"
                  placeholder="yourcompany"
                  required
                  disabled={!isEditing}
                />
                <span className="ml-2 text-foreground/70">.zendesk.com</span>
              </div>
            </div>
            
            <div>
              <label htmlFor="config.email" className="block text-sm font-medium mb-1">
                Email <span className="text-error">*</span>
              </label>
              <input
                type="email"
                id="config.email"
                name="config.email"
                value={formState.config?.email || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-foreground/10 rounded-lg bg-foreground/5"
                placeholder="<EMAIL>"
                required
                disabled={!isEditing}
              />
            </div>
            
            <div>
              <label htmlFor="config.apiToken" className="block text-sm font-medium mb-1">
                API Token <span className="text-error">*</span>
              </label>
              <input
                type="password"
                id="config.apiToken"
                name="config.apiToken"
                value={formState.config?.apiToken || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-foreground/10 rounded-lg bg-foreground/5"
                placeholder={isEditing ? "Enter new token or leave blank to keep current" : "••••••••••••••••"}
                disabled={!isEditing}
              />
            </div>
          </div>
        );
      
      case 'hubspot':
        return (
          <div className="space-y-4">
            <div>
              <label htmlFor="config.apiKey" className="block text-sm font-medium mb-1">
                API Key <span className="text-error">*</span>
              </label>
              <input
                type="password"
                id="config.apiKey"
                name="config.apiKey"
                value={formState.config?.apiKey || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-foreground/10 rounded-lg bg-foreground/5"
                placeholder={isEditing ? "Enter new key or leave blank to keep current" : "••••••••••••••••"}
                disabled={!isEditing}
              />
            </div>
            
            <div>
              <label htmlFor="config.contactId" className="block text-sm font-medium mb-1">
                Default Contact ID (Optional)
              </label>
              <input
                type="text"
                id="config.contactId"
                name="config.contactId"
                value={formState.config?.contactId || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-foreground/10 rounded-lg bg-foreground/5"
                placeholder="123456789"
                disabled={!isEditing}
              />
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };
  
  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </main>
      </div>
    );
  }
  
  if (error || !integration) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="card py-12 text-center">
            <h2 className="text-xl font-medium mb-4 text-error">Error</h2>
            <p className="text-foreground/70 mb-6">{error || 'Integration not found'}</p>
            <Link href="/integrations" className="btn-primary inline-block">
              Back to Integrations
            </Link>
          </div>
        </main>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="flex items-center gap-2 mb-8">
            <Link href="/integrations" className="text-foreground/70 hover:text-foreground">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                <path d="M19 12H5M12 19l-7-7 7-7" />
              </svg>
            </Link>
            <h1 className="text-3xl font-semibold font-heading">{integration.name}</h1>
            <span className={`ml-auto px-2 py-0.5 rounded-full text-xs ${
              integration.enabled
                ? 'bg-success/10 text-success'
                : 'bg-foreground/10 text-foreground/70'
            }`}>
              {integration.enabled ? 'Enabled' : 'Disabled'}
            </span>
          </div>
          
          <div className="card">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium mb-1">
                  Integration Name <span className="text-error">*</span>
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formState.name || ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-foreground/10 rounded-lg bg-foreground/5"
                  placeholder="My Integration"
                  required
                  disabled={!isEditing}
                />
              </div>
              
              <div>
                <label htmlFor="type" className="block text-sm font-medium mb-1">
                  Integration Type
                </label>
                <input
                  type="text"
                  id="type"
                  value={integration.type.charAt(0).toUpperCase() + integration.type.slice(1)}
                  className="w-full px-3 py-2 border border-foreground/10 rounded-lg bg-foreground/5"
                  disabled
                />
              </div>
              
              <div className="border-t border-foreground/10 pt-6">
                <h2 className="text-lg font-medium mb-4">Configuration</h2>
                {renderFormFields()}
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enabled"
                  name="enabled"
                  checked={formState.enabled || false}
                  onChange={handleCheckboxChange}
                  className="h-4 w-4 text-primary border-foreground/30 rounded"
                  disabled={!isEditing}
                />
                <label htmlFor="enabled" className="ml-2 block text-sm">
                  Enable this integration
                </label>
              </div>
              
              <div className="text-sm text-foreground/70">
                <p>Created: {formatDate(integration.createdAt)}</p>
                <p>Last Updated: {formatDate(integration.updatedAt)}</p>
              </div>
              
              {saveError && (
                <div className="bg-error/10 text-error px-4 py-3 rounded-lg text-sm">
                  {saveError}
                </div>
              )}
              
              {testResult && (
                <div className={`${
                  testResult.success ? 'bg-success/10 text-success' : 'bg-error/10 text-error'
                } px-4 py-3 rounded-lg text-sm`}>
                  {testResult.message}
                </div>
              )}
              
              <div className="flex gap-4 pt-2">
                {isEditing ? (
                  <>
                    <button
                      type="submit"
                      className="btn-primary flex-1"
                      disabled={saveLoading}
                    >
                      {saveLoading ? 'Saving...' : 'Save Changes'}
                    </button>
                    
                    <button
                      type="button"
                      onClick={() => setIsEditing(false)}
                      className="btn-secondary"
                      disabled={saveLoading}
                    >
                      Cancel
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      type="button"
                      onClick={() => setIsEditing(true)}
                      className="btn-primary flex-1"
                    >
                      Edit Integration
                    </button>
                    
                    <button
                      type="button"
                      onClick={handleTest}
                      className="btn-secondary"
                      disabled={saveLoading}
                    >
                      Test Connection
                    </button>
                    
                    <button
                      type="button"
                      onClick={handleDelete}
                      className={`${
                        deleteConfirm ? 'bg-error text-white' : 'bg-foreground/10 text-foreground/70'
                      } px-4 py-2 rounded-lg hover:bg-opacity-90 transition-colors`}
                      disabled={saveLoading}
                    >
                      {deleteConfirm ? 'Confirm Delete' : 'Delete'}
                    </button>
                  </>
                )}
              </div>
            </form>
          </div>
        </div>
      </main>
    </div>
  );
}
