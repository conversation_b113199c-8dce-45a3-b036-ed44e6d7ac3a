import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { AccessToken } from 'livekit-server-sdk';

interface TokenRequest {
  roomName: string;
  participantName: string;
  participantIdentity?: string;
}

export default async function (fastify: FastifyInstance) {
  // Authentication middleware
  fastify.addHook('onRequest', async (request, reply) => {
    try {
      // Skip authentication for development
      if (process.env.NODE_ENV === 'development') {
        return;
      }
      
      await request.jwtVerify();
    } catch (err) {
      reply.send(err);
    }
  });

  // Generate a LiveKit token
  fastify.post('/token', async (request: FastifyRequest<{ Body: TokenRequest }>, reply: FastifyReply) => {
    const { roomName, participantName, participantIdentity } = request.body;

    if (!roomName || !participantName) {
      return reply.status(400).send({ error: 'Room name and participant name are required' });
    }

    try {
      const apiKey = process.env.LIVEKIT_API_KEY;
      const apiSecret = process.env.LIVEKIT_API_SECRET;

      if (!apiKey || !apiSecret) {
        return reply.status(500).send({ error: 'LiveKit API key or secret not configured' });
      }

      // Create a new token
      const token = new AccessToken(apiKey, apiSecret, {
        identity: participantIdentity || `${participantName}-${Date.now()}`,
        name: participantName,
      });

      // Grant permissions
      token.addGrant({
        roomJoin: true,
        room: roomName,
        canPublish: true,
        canSubscribe: true,
        canPublishData: true,
      });

      // Return the token
      return reply.send({
        token: token.toJwt(),
        roomName,
        participantName,
        participantIdentity: token.identity,
      });
    } catch (error) {
      console.error('Error generating LiveKit token:', error);
      return reply.status(500).send({ error: 'Failed to generate token' });
    }
  });

  // Start room recording
  fastify.post('/rooms/:roomName/record/start', async (request: FastifyRequest<{ Params: { roomName: string } }>, reply: FastifyReply) => {
    const { roomName } = request.params;

    try {
      // In a real implementation, we would call the LiveKit server API to start recording
      // For now, we'll just return a success message
      return reply.send({
        success: true,
        message: `Started recording room: ${roomName}`,
        roomName,
      });
    } catch (error) {
      console.error('Error starting room recording:', error);
      return reply.status(500).send({ error: 'Failed to start recording' });
    }
  });

  // Stop room recording
  fastify.post('/rooms/:roomName/record/stop', async (request: FastifyRequest<{ Params: { roomName: string } }>, reply: FastifyReply) => {
    const { roomName } = request.params;

    try {
      // In a real implementation, we would call the LiveKit server API to stop recording
      // For now, we'll just return a success message
      return reply.send({
        success: true,
        message: `Stopped recording room: ${roomName}`,
        roomName,
        recordingUrl: `https://example.com/recordings/${roomName}-${Date.now()}.mp4`,
      });
    } catch (error) {
      console.error('Error stopping room recording:', error);
      return reply.status(500).send({ error: 'Failed to stop recording' });
    }
  });
}
