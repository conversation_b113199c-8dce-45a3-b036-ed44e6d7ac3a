import { PrismaClient } from '@prisma/client';
import fetch from 'node-fetch';

const prisma = new PrismaClient();

interface WebhookPayload {
  event: string;
  data: any;
  timestamp: string;
}

/**
 * Send a webhook to a destination URL
 */
export async function sendWebhook(
  url: string,
  payload: WebhookPayload,
  headers: Record<string, string> = {}
): Promise<{ success: boolean; statusCode: number; responseTime: number }> {
  const startTime = Date.now();
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
      body: JSON.stringify(payload),
    });
    
    const responseTime = Date.now() - startTime;
    
    return {
      success: response.ok,
      statusCode: response.status,
      responseTime,
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    console.error(`Error sending webhook to ${url}:`, error);
    
    return {
      success: false,
      statusCode: 0,
      responseTime,
    };
  }
}

/**
 * Process action rules for a video
 */
export async function processVideoRules(videoId: string, event: string): Promise<void> {
  try {
    // Get the video with tenant and uploader
    const video = await prisma.video.findUnique({
      where: { id: videoId },
      include: {
        tenant: true,
        uploader: true,
        transcript: {
          include: {
            chunks: true,
          },
        },
      },
    });
    
    if (!video) {
      throw new Error(`Video ${videoId} not found`);
    }
    
    // Find action rules for this tenant and event
    const rules = await prisma.actionRule.findMany({
      where: {
        tenantId: video.tenantId,
        trigger: {
          path: ['event'],
          equals: event,
        },
      },
    });
    
    if (rules.length === 0) {
      console.log(`No action rules found for video ${videoId} and event ${event}`);
      return;
    }
    
    console.log(`Processing ${rules.length} action rules for video ${videoId} and event ${event}`);
    
    // Process each rule
    for (const rule of rules) {
      try {
        // Prepare payload using template
        const payload = preparePayload(rule.payloadTemplate, {
          video,
          event,
          timestamp: new Date().toISOString(),
        });
        
        // Send webhook
        const result = await sendWebhook(rule.destination, payload);
        
        // Log the action
        await prisma.actionLog.create({
          data: {
            ruleId: rule.id,
            videoId: video.id,
            status: result.success ? 'sent' : 'failed',
            responseMs: result.responseTime,
          },
        });
        
        console.log(`Webhook for rule ${rule.id} sent with status ${result.success ? 'success' : 'failed'}`);
      } catch (error) {
        console.error(`Error processing rule ${rule.id}:`, error);
        
        // Log the failed action
        await prisma.actionLog.create({
          data: {
            ruleId: rule.id,
            videoId: video.id,
            status: 'failed',
            responseMs: 0,
          },
        });
      }
    }
  } catch (error) {
    console.error(`Error processing rules for video ${videoId}:`, error);
  }
}

/**
 * Prepare webhook payload using template
 */
function preparePayload(template: string, data: any): WebhookPayload {
  try {
    // Parse the template
    const parsedTemplate = JSON.parse(template);
    
    // Replace placeholders with actual values
    const replacePlaceholders = (obj: any): any => {
      if (typeof obj === 'string') {
        return replacePlaceholderString(obj, data);
      } else if (Array.isArray(obj)) {
        return obj.map(item => replacePlaceholders(item));
      } else if (obj !== null && typeof obj === 'object') {
        const result: Record<string, any> = {};
        for (const key in obj) {
          result[key] = replacePlaceholders(obj[key]);
        }
        return result;
      }
      return obj;
    };
    
    return replacePlaceholders(parsedTemplate);
  } catch (error) {
    console.error('Error preparing payload:', error);
    
    // Return a basic payload if template parsing fails
    return {
      event: data.event,
      data: {
        videoId: data.video.id,
        error: 'Failed to parse template',
      },
      timestamp: data.timestamp,
    };
  }
}

/**
 * Replace placeholders in a string
 */
function replacePlaceholderString(str: string, data: any): string {
  return str.replace(/\{\{([^}]+)\}\}/g, (match, path) => {
    const value = getValueByPath(data, path.trim());
    return value !== undefined ? String(value) : match;
  });
}

/**
 * Get a value from an object by path
 */
function getValueByPath(obj: any, path: string): any {
  const parts = path.split('.');
  let current = obj;
  
  for (const part of parts) {
    if (current === null || current === undefined) {
      return undefined;
    }
    current = current[part];
  }
  
  return current;
}

export default {
  sendWebhook,
  processVideoRules,
};
