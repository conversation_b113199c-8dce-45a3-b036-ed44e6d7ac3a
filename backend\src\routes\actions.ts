import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { PrismaClient } from '@prisma/client';
import { verifyAuth } from '../middleware/auth';
import { processVideoRules } from '../services/webhook';

const prisma = new PrismaClient();

interface AuthenticatedRequest extends FastifyRequest {
  user?: {
    id: string;
    clerkId: string;
    email: string;
    name: string;
    tenantId: string;
  };
}

interface ActionRuleParams {
  id: string;
}

interface CreateActionRuleBody {
  name: string;
  trigger: {
    event: string;
    conditions?: Record<string, any>[];
  };
  destination: string;
  payloadTemplate: string;
}

interface UpdateActionRuleBody {
  name?: string;
  trigger?: {
    event: string;
    conditions?: Record<string, any>[];
  };
  destination?: string;
  payloadTemplate?: string;
}

export default async function (fastify: FastifyInstance) {
  // Authentication middleware
  fastify.addHook('onRequest', verifyAuth);

  // Get all action rules for the tenant
  fastify.get('/', async (request: AuthenticatedRequest, reply: FastifyReply) => {
    if (!request.user) {
      return reply.status(401).send({ error: 'Unauthorized' });
    }

    const { tenantId } = request.user;

    try {
      const rules = await prisma.actionRule.findMany({
        where: { tenantId },
        orderBy: { createdAt: 'desc' },
      });

      return reply.send(rules);
    } catch (error) {
      console.error('Error fetching action rules:', error);
      return reply.status(500).send({ error: 'Failed to fetch action rules' });
    }
  });

  // Get a single action rule by ID
  fastify.get('/:id', async (request: AuthenticatedRequest<{ Params: ActionRuleParams }>, reply: FastifyReply) => {
    if (!request.user) {
      return reply.status(401).send({ error: 'Unauthorized' });
    }

    const { id } = request.params;
    const { tenantId } = request.user;

    try {
      const rule = await prisma.actionRule.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!rule) {
        return reply.status(404).send({ error: 'Action rule not found' });
      }

      return reply.send(rule);
    } catch (error) {
      console.error('Error fetching action rule:', error);
      return reply.status(500).send({ error: 'Failed to fetch action rule' });
    }
  });

  // Create a new action rule
  fastify.post('/', async (request: AuthenticatedRequest<{ Body: CreateActionRuleBody }>, reply: FastifyReply) => {
    if (!request.user) {
      return reply.status(401).send({ error: 'Unauthorized' });
    }

    const { name, trigger, destination, payloadTemplate } = request.body;
    const { tenantId } = request.user;

    try {
      const rule = await prisma.actionRule.create({
        data: {
          name,
          trigger,
          destination,
          payloadTemplate,
          tenantId,
        },
      });

      return reply.status(201).send(rule);
    } catch (error) {
      console.error('Error creating action rule:', error);
      return reply.status(500).send({ error: 'Failed to create action rule' });
    }
  });

  // Update an action rule
  fastify.patch('/:id', async (request: AuthenticatedRequest<{ Params: ActionRuleParams; Body: UpdateActionRuleBody }>, reply: FastifyReply) => {
    if (!request.user) {
      return reply.status(401).send({ error: 'Unauthorized' });
    }

    const { id } = request.params;
    const { name, trigger, destination, payloadTemplate } = request.body;
    const { tenantId } = request.user;

    try {
      // Check if the rule exists and belongs to the tenant
      const existingRule = await prisma.actionRule.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!existingRule) {
        return reply.status(404).send({ error: 'Action rule not found' });
      }

      // Update the rule
      const rule = await prisma.actionRule.update({
        where: { id },
        data: {
          name,
          trigger,
          destination,
          payloadTemplate,
        },
      });

      return reply.send(rule);
    } catch (error) {
      console.error('Error updating action rule:', error);
      return reply.status(500).send({ error: 'Failed to update action rule' });
    }
  });

  // Delete an action rule
  fastify.delete('/:id', async (request: AuthenticatedRequest<{ Params: ActionRuleParams }>, reply: FastifyReply) => {
    if (!request.user) {
      return reply.status(401).send({ error: 'Unauthorized' });
    }

    const { id } = request.params;
    const { tenantId } = request.user;

    try {
      // Check if the rule exists and belongs to the tenant
      const existingRule = await prisma.actionRule.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!existingRule) {
        return reply.status(404).send({ error: 'Action rule not found' });
      }

      // Delete the rule
      await prisma.actionRule.delete({
        where: { id },
      });

      return reply.status(204).send();
    } catch (error) {
      console.error('Error deleting action rule:', error);
      return reply.status(500).send({ error: 'Failed to delete action rule' });
    }
  });

  // Manually trigger a rule for a video
  fastify.post('/trigger/:ruleId/:videoId', async (request: AuthenticatedRequest<{ Params: { ruleId: string; videoId: string } }>, reply: FastifyReply) => {
    if (!request.user) {
      return reply.status(401).send({ error: 'Unauthorized' });
    }

    const { ruleId, videoId } = request.params;
    const { tenantId } = request.user;

    try {
      // Check if the rule exists and belongs to the tenant
      const rule = await prisma.actionRule.findFirst({
        where: {
          id: ruleId,
          tenantId,
        },
      });

      if (!rule) {
        return reply.status(404).send({ error: 'Action rule not found' });
      }

      // Check if the video exists and belongs to the tenant
      const video = await prisma.video.findFirst({
        where: {
          id: videoId,
          tenantId,
        },
      });

      if (!video) {
        return reply.status(404).send({ error: 'Video not found' });
      }

      // Get the event from the rule trigger
      const event = rule.trigger.event || 'manual';

      // Process the rule
      await processVideoRules(videoId, event);

      return reply.send({ success: true, message: 'Rule triggered successfully' });
    } catch (error) {
      console.error('Error triggering rule:', error);
      return reply.status(500).send({ error: 'Failed to trigger rule' });
    }
  });
}
