{"name": "cluster-key-slot", "version": "1.1.2", "description": "Generates CRC hashes for strings - for use by node redis clients to determine key slots.", "main": "lib/index.js", "types": "index.d.ts", "scripts": {"benchmark": "node ./benchmark", "posttest": "eslint ./lib && npm run coveralls", "coveralls": "cat ./coverage/lcov.info | coveralls", "test": "node ./node_modules/istanbul/lib/cli.js cover --preserve-comments ./node_modules/mocha/bin/_mocha -- -R spec", "coverage:check": "node ./node_modules/istanbul/lib/cli.js check-coverage --branch 100 --statement 100"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/cluster-key-slot.git"}, "keywords": ["redis", "hash", "crc", "slot", "calc", "javascript", "node", "node_redis", "i<PERSON>is"], "engines": {"node": ">=0.10.0"}, "devDependencies": {"benchmark": "^2.1.0", "codeclimate-test-reporter": "^0.3.1", "coveralls": "^2.11.9", "eslint": "^3.5.0", "eslint-config-airbnb-base": "^7.1.0", "eslint-plugin-import": "^1.8.0", "istanbul": "^0.4.0", "mocha": "^3.0.2"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Salakar/cluster-key-slot/issues"}, "homepage": "https://github.com/Salakar/cluster-key-slot#readme", "directories": {"test": "test", "lib": "lib"}}