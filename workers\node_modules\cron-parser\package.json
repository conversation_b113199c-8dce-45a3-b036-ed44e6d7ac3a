{"name": "cron-parser", "version": "4.9.0", "description": "Node.js library for parsing crontab instructions", "main": "lib/parser.js", "types": "types/index.d.ts", "typesVersions": {"<4.1": {"*": ["types/ts3/*"]}}, "directories": {"test": "test"}, "scripts": {"test:tsd": "tsd", "test:unit": "TZ=UTC tap ./test/*.js", "test:cover": "TZ=UTC tap --coverage-report=html ./test/*.js", "lint": "eslint .", "lint:fix": "eslint --fix .", "test": "npm run lint && npm run test:unit && npm run test:tsd"}, "repository": {"type": "git", "url": "https://github.com/harrisiirak/cron-parser.git"}, "keywords": ["cron", "crontab", "parser"], "author": "<PERSON><PERSON>", "contributors": ["<PERSON>", "<PERSON> <<EMAIL>>", "Renault John <PERSON>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Santiago Gimeno <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <yasu<PERSON><EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "dependencies": {"luxon": "^3.2.1"}, "devDependencies": {"eslint": "^8.27.0", "sinon": "^15.0.1", "tap": "^16.3.3", "tsd": "^0.26.0"}, "engines": {"node": ">=12.0.0"}, "browser": {"fs": false}, "tap": {"check-coverage": false}, "tsd": {"directory": "test", "compilerOptions": {"lib": ["es2017", "dom"]}}, "files": ["lib", "types", "LICENSE", "README.md"]}