'use client';

import axios from 'axios';
import { auth } from '@clerk/nextjs';

// API base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add authentication token to requests if available
api.interceptors.request.use(async (config) => {
  try {
    // Get the session token from Clerk
    const { getToken } = auth();
    const token = await getToken();

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
  } catch (error) {
    console.error('Error getting auth token:', error);
  }

  return config;
});

// API endpoints for videos
export const videoApi = {
  // Get all videos for a tenant
  getVideos: async (tenantId: string) => {
    const response = await api.get(`/videos?tenantId=${tenantId}`);
    return response.data;
  },

  // Get a single video by ID
  getVideo: async (id: string) => {
    const response = await api.get(`/videos/${id}`);
    return response.data;
  },

  // Create a new video
  createVideo: async (data: {
    tenantId: string;
    uploaderId: string;
    storageUrl: string;
    thumbnailUrl?: string;
    durationSec?: number;
    sizeMb?: number;
  }) => {
    const response = await api.post('/videos', data);
    return response.data;
  },

  // Update a video
  updateVideo: async (id: string, data: {
    status?: string;
    thumbnailUrl?: string;
    durationSec?: number;
    sizeMb?: number;
  }) => {
    const response = await api.patch(`/videos/${id}`, data);
    return response.data;
  },

  // Delete a video
  deleteVideo: async (id: string) => {
    await api.delete(`/videos/${id}`);
  },

  // Record a view for a video
  recordView: async (id: string, data: { viewerEmail?: string; ip?: string }) => {
    const response = await api.post(`/videos/${id}/views`, data);
    return response.data;
  },
};

// API endpoints for LiveKit
export const livekitApi = {
  // Get a token for a LiveKit room
  getToken: async (roomName: string, participantName: string) => {
    const response = await api.post('/livekit/token', {
      roomName,
      participantName,
    });
    return response.data;
  },

  // Start recording a room
  startRecording: async (roomName: string) => {
    const response = await api.post(`/livekit/rooms/${roomName}/record/start`);
    return response.data;
  },

  // Stop recording a room
  stopRecording: async (roomName: string) => {
    const response = await api.post(`/livekit/rooms/${roomName}/record/stop`);
    return response.data;
  },
};

export default api;
