"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpanKind = exports.TelemetryAttributes = void 0;
var TelemetryAttributes;
(function (TelemetryAttributes) {
    TelemetryAttributes["QueueName"] = "bullmq.queue.name";
    TelemetryAttributes["QueueOperation"] = "bullmq.queue.operation";
    TelemetryAttributes["BulkCount"] = "bullmq.job.bulk.count";
    TelemetryAttributes["BulkNames"] = "bullmq.job.bulk.names";
    TelemetryAttributes["JobName"] = "bullmq.job.name";
    TelemetryAttributes["JobId"] = "bullmq.job.id";
    TelemetryAttributes["JobKey"] = "bullmq.job.key";
    TelemetryAttributes["JobIds"] = "bullmq.job.ids";
    TelemetryAttributes["DeduplicationKey"] = "bullmq.job.deduplication.key";
    TelemetryAttributes["JobOptions"] = "bullmq.job.options";
    TelemetryAttributes["JobProgress"] = "bullmq.job.progress";
    TelemetryAttributes["QueueDrainDelay"] = "bullmq.queue.drain.delay";
    TelemetryAttributes["QueueGrace"] = "bullmq.queue.grace";
    TelemetryAttributes["QueueCleanLimit"] = "bullmq.queue.clean.limit";
    TelemetryAttributes["QueueRateLimit"] = "bullmq.queue.rate.limit";
    TelemetryAttributes["JobType"] = "bullmq.job.type";
    TelemetryAttributes["QueueOptions"] = "bullmq.queue.options";
    TelemetryAttributes["QueueEventMaxLength"] = "bullmq.queue.event.max.length";
    TelemetryAttributes["WorkerOptions"] = "bullmq.worker.options";
    TelemetryAttributes["WorkerName"] = "bullmq.worker.name";
    TelemetryAttributes["WorkerId"] = "bullmq.worker.id";
    TelemetryAttributes["WorkerRateLimit"] = "bullmq.worker.rate.limit";
    TelemetryAttributes["WorkerDoNotWaitActive"] = "bullmq.worker.do.not.wait.active";
    TelemetryAttributes["WorkerForceClose"] = "bullmq.worker.force.close";
    TelemetryAttributes["WorkerStalledJobs"] = "bullmq.worker.stalled.jobs";
    TelemetryAttributes["WorkerFailedJobs"] = "bullmq.worker.failed.jobs";
    TelemetryAttributes["WorkerJobsToExtendLocks"] = "bullmq.worker.jobs.to.extend.locks";
    TelemetryAttributes["JobFinishedTimestamp"] = "bullmq.job.finished.timestamp";
    TelemetryAttributes["JobProcessedTimestamp"] = "bullmq.job.processed.timestamp";
    TelemetryAttributes["JobResult"] = "bullmq.job.result";
    TelemetryAttributes["JobFailedReason"] = "bullmq.job.failed.reason";
    TelemetryAttributes["FlowName"] = "bullmq.flow.name";
    TelemetryAttributes["JobSchedulerId"] = "bullmq.job.scheduler.id";
})(TelemetryAttributes = exports.TelemetryAttributes || (exports.TelemetryAttributes = {}));
var SpanKind;
(function (SpanKind) {
    SpanKind[SpanKind["INTERNAL"] = 0] = "INTERNAL";
    SpanKind[SpanKind["SERVER"] = 1] = "SERVER";
    SpanKind[SpanKind["CLIENT"] = 2] = "CLIENT";
    SpanKind[SpanKind["PRODUCER"] = 3] = "PRODUCER";
    SpanKind[SpanKind["CONSUMER"] = 4] = "CONSUMER";
})(SpanKind = exports.SpanKind || (exports.SpanKind = {}));
//# sourceMappingURL=telemetry-attributes.js.map