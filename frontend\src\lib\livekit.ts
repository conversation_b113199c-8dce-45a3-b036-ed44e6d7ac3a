'use client';

import { Room, RoomEvent, LocalParticipant, RemoteParticipant, Track } from 'livekit-client';

// LiveKit server URL
const LIVEKIT_URL = process.env.NEXT_PUBLIC_LIVEKIT_URL || 'ws://localhost:7880';

/**
 * Creates and connects to a LiveKit room
 * @param roomName The name of the room to connect to
 * @param token The authentication token
 * @returns A connected Room instance
 */
export async function connectToRoom(roomName: string, token: string): Promise<Room> {
  const room = new Room({
    adaptiveStream: true,
    dynacast: true,
    videoCaptureDefaults: {
      resolution: { width: 1280, height: 720 },
    },
  });

  try {
    await room.connect(LIVEKIT_URL, token);
    console.log('Connected to LiveKit room:', roomName);
    return room;
  } catch (error) {
    console.error('Error connecting to LiveKit room:', error);
    throw error;
  }
}

/**
 * Starts local video and audio tracks and publishes them to the room
 * @param room The LiveKit room
 * @returns The local participant
 */
export async function startLocalTracks(room: Room): Promise<LocalParticipant> {
  const localParticipant = room.localParticipant;

  try {
    // Request camera and microphone permissions
    await localParticipant.enableCameraAndMicrophone();
    console.log('Local tracks started');
    return localParticipant;
  } catch (error) {
    console.error('Error starting local tracks:', error);
    throw error;
  }
}

/**
 * Starts recording the room
 * @param room The LiveKit room
 * @returns Promise that resolves when recording starts
 */
export async function startRecording(room: Room): Promise<void> {
  try {
    // In a real implementation, we would call the LiveKit server API to start recording
    // For now, we'll just log a message
    console.log('Started recording room:', room.name);
  } catch (error) {
    console.error('Error starting recording:', error);
    throw error;
  }
}

/**
 * Stops recording the room
 * @param room The LiveKit room
 * @returns Promise that resolves when recording stops
 */
export async function stopRecording(room: Room): Promise<void> {
  try {
    // In a real implementation, we would call the LiveKit server API to stop recording
    // For now, we'll just log a message
    console.log('Stopped recording room:', room.name);
  } catch (error) {
    console.error('Error stopping recording:', error);
    throw error;
  }
}

/**
 * Disconnects from the LiveKit room and cleans up resources
 * @param room The LiveKit room
 */
export function disconnectFromRoom(room: Room): void {
  try {
    room.disconnect();
    console.log('Disconnected from LiveKit room');
  } catch (error) {
    console.error('Error disconnecting from LiveKit room:', error);
  }
}
