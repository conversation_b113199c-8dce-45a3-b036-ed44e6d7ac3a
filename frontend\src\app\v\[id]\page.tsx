'use client';

import { useState, useEffect } from 'react';
import { formatDate, formatTime } from '@/lib/utils';

interface VideoPageProps {
  params: {
    id: string;
  };
}

interface VideoData {
  id: string;
  title: string;
  status: string;
  durationSec?: number;
  sizeMb?: number;
  createdAt: string;
  uploader: {
    name: string;
    email: string;
  };
  sentiment?: 'positive' | 'neutral' | 'negative';
  sentimentConfidence?: number;
  summary?: string;
  keyPoints?: string[];
  transcript?: {
    id: string;
    language: string;
    confidence: number;
    chunks: Array<{
      id: string;
      startMs: number;
      endMs: number;
      text: string;
    }>;
  };
}

export default function VideoPage({ params }: VideoPageProps) {
  const { id } = params;
  const [videoData, setVideoData] = useState<VideoData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchVideoData() {
      try {
        const response = await fetch(`/api/videos/${id}`);

        if (!response.ok) {
          throw new Error('Failed to fetch video');
        }

        const data = await response.json();
        setVideoData(data);
      } catch (error) {
        console.error('Error fetching video:', error);
        setError('Failed to load video data');
      } finally {
        setLoading(false);
      }
    }

    fetchVideoData();
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-semibold mb-4 text-error">Error</h1>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  if (!videoData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-semibold mb-4">Video Not Found</h1>
          <p>The video you're looking for doesn't exist or has been removed.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-semibold font-heading mb-2">{videoData.title}</h1>
            <div className="flex items-center gap-4 text-sm text-foreground/70">
              <span>{formatDate(videoData.createdAt)}</span>
              <span>•</span>
              <span>{formatTime(videoData.duration)}</span>
              <span>•</span>
              <span>By {videoData.uploader.name}</span>
            </div>
          </div>

          <div className="bg-black rounded-lg overflow-hidden mb-8 aspect-video flex items-center justify-center">
            {/* Placeholder for video player */}
            <div className="text-white text-center p-8">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-16 w-16 mx-auto mb-4"
              >
                <circle cx="12" cy="12" r="10" />
                <polygon points="10 8 16 12 10 16 10 8" />
              </svg>
              <p className="text-lg">Video Player Placeholder</p>
              <p className="text-sm opacity-70 mt-2">
                In a real implementation, this would be a video player showing the content.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              <div className="card mb-6">
                <h2 className="text-lg font-medium mb-4">Transcript</h2>
                {videoData.status === 'processing' ? (
                  <div className="flex items-center gap-2 text-foreground/70">
                    <svg
                      className="animate-spin h-4 w-4 text-primary"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    <span>Processing transcript...</span>
                  </div>
                ) : videoData.transcript && videoData.transcript.chunks && videoData.transcript.chunks.length > 0 ? (
                  <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
                    {videoData.transcript.chunks.map((chunk) => (
                      <div key={chunk.id} className="pb-2 border-b border-foreground/10 last:border-0">
                        <div className="text-xs text-foreground/50 mb-1">
                          {formatTime(chunk.startMs / 1000)} - {formatTime(chunk.endMs / 1000)}
                        </div>
                        <p>{chunk.text}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-foreground/70 text-sm italic">
                    Transcript will be available once the video is processed.
                  </p>
                )}
              </div>
            </div>

            <div>
              <div className="card mb-6">
                <h2 className="text-lg font-medium mb-4">Summary</h2>
                {videoData.status === 'processing' ? (
                  <div className="flex items-center gap-2 text-foreground/70">
                    <svg
                      className="animate-spin h-4 w-4 text-primary"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    <span>Generating summary...</span>
                  </div>
                ) : videoData.summary ? (
                  <div>
                    <p className="mb-4">{videoData.summary}</p>

                    {videoData.keyPoints && videoData.keyPoints.length > 0 && (
                      <div>
                        <h3 className="text-sm font-medium mb-2">Key Points:</h3>
                        <ul className="list-disc pl-5 space-y-1">
                          {videoData.keyPoints.map((point, index) => (
                            <li key={index}>{point}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-foreground/70 text-sm italic">
                    AI-generated summary will appear here after processing.
                  </p>
                )}
              </div>

              <div className="card">
                <h2 className="text-lg font-medium mb-4">Sentiment</h2>
                {videoData.status === 'processing' ? (
                  <div className="flex items-center gap-2 text-foreground/70">
                    <svg
                      className="animate-spin h-4 w-4 text-primary"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    <span>Analyzing sentiment...</span>
                  </div>
                ) : videoData.sentiment ? (
                  <div className="flex items-center gap-2">
                    <div className={`kpi-chip ${
                      videoData.sentiment === 'positive' ? 'kpi-chip-success' :
                      videoData.sentiment === 'negative' ? 'kpi-chip-error' :
                      'kpi-chip-warning'
                    }`}>
                      {videoData.sentiment.charAt(0).toUpperCase() + videoData.sentiment.slice(1)}
                    </div>

                    {videoData.sentimentConfidence && (
                      <span className="text-sm text-foreground/70">
                        {Math.round(videoData.sentimentConfidence * 100)}% confidence
                      </span>
                    )}
                  </div>
                ) : (
                  <p className="text-foreground/70 text-sm italic">
                    Sentiment analysis will appear here after processing.
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
