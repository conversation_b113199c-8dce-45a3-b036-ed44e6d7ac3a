@import "tailwindcss";

:root {
  /* EchoPilot Color Palette */
  --background: #F8FAFC;
  --foreground: #0F172A;
  --primary: #0EA5E9;
  --primary-hover: #60A5FA;
  --accent: #FACC15;
  --error: #EF4444;
  --warning: #F59E0B;
  --success: #10B981;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-hover: var(--primary-hover);
  --color-accent: var(--accent);
  --color-error: var(--error);
  --color-warning: var(--warning);
  --color-success: var(--success);
  --font-sans: var(--font-inter);
  --font-heading: var(--font-poppins);
}

.dark {
  --background: #0F172A;
  --foreground: #F8FAFC;
}

@layer base {
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-sans);
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary text-white hover:bg-primary-hover rounded-full px-4 py-2 font-medium transition-colors;
  }

  .btn-secondary {
    @apply border border-foreground/10 hover:bg-foreground/5 rounded-full px-4 py-2 font-medium transition-colors;
  }

  .card {
    @apply bg-white dark:bg-foreground/10 rounded-2xl shadow-md p-6;
  }

  .kpi-chip {
    @apply rounded-full px-3 py-1 text-sm font-medium;
  }

  .kpi-chip-success {
    @apply bg-gradient-to-r from-primary to-primary-hover text-white;
  }

  .kpi-chip-warning {
    @apply bg-gradient-to-r from-warning to-accent text-foreground;
  }

  .kpi-chip-error {
    @apply bg-gradient-to-r from-error to-warning text-white;
  }
}
