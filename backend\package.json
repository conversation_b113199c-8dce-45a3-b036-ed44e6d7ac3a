{"name": "echopilot-backend", "version": "0.1.0", "description": "Backend API for EchoPilot video messaging platform", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["video", "messaging", "ai", "livekit"], "author": "", "license": "ISC", "dependencies": {"@clerk/backend": "^1.32.3", "@fastify/cors": "^11.0.1", "@fastify/jwt": "^9.1.0", "@fastify/static": "^8.1.1", "@prisma/client": "^6.8.2", "bullmq": "^5.52.3", "dotenv": "^16.5.0", "fastify": "^5.3.3", "livekit-server-sdk": "^2.13.0", "prisma": "^6.8.2", "tus-node-server": "^0.9.0"}, "devDependencies": {"@types/node": "^22.15.19", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}