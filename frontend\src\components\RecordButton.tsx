'use client';

import { useState, useRef, useEffect } from 'react';
import { cn, formatTime } from '@/lib/utils';
import { Room } from 'livekit-client';
import { connectToRoom, startLocalTracks, startRecording, stopRecording, disconnectFromRoom } from '@/lib/livekit';

interface RecordButtonProps {
  onRecordingComplete: (recordingUrl: string) => void;
  maxDuration?: number; // in seconds
  className?: string;
}

export function RecordButton({
  onRecordingComplete,
  maxDuration = 300, // 5 minutes default
  className,
}: RecordButtonProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [duration, setDuration] = useState(0);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);

  const roomRef = useRef<Room | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Check for media permissions
  useEffect(() => {
    async function checkPermissions() {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
        setHasPermission(true);

        // Stop the stream immediately after permission check
        stream.getTracks().forEach(track => track.stop());
      } catch (err) {
        console.error('Error checking permissions:', err);
        setHasPermission(false);
        setError('Camera or microphone access denied');
      }
    }

    checkPermissions();

    // Cleanup function
    return () => {
      if (roomRef.current) {
        disconnectFromRoom(roomRef.current);
        roomRef.current = null;
      }

      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  const startRecording = async () => {
    try {
      setError(null);
      setIsConnecting(true);

      // Generate a unique room name
      const roomName = `recording-${Date.now()}`;

      // In a real implementation, we would fetch a token from the server
      // For now, we'll use a mock token since we don't have authentication set up yet
      const mockToken = 'mock-token';

      // Connect to LiveKit room
      // In a real implementation, we would use:
      // const { token } = await livekitApi.getToken(roomName, 'user');
      // const room = await connectToRoom(roomName, token);
      const room = await connectToRoom(roomName, mockToken);
      roomRef.current = room;

      // Start local tracks
      await startLocalTracks(room);

      // Start recording
      await startRecording(room);

      setIsConnecting(false);
      setIsRecording(true);

      // Start timer
      setDuration(0);
      timerRef.current = setInterval(() => {
        setDuration(prev => {
          const newDuration = prev + 1;

          // Stop recording if max duration is reached
          if (newDuration >= maxDuration) {
            stopRecordingHandler();
          }

          return newDuration;
        });
      }, 1000);

    } catch (err) {
      console.error('Error starting recording:', err);
      setError('Failed to start recording');
      setIsConnecting(false);
    }
  };

  const stopRecordingHandler = async () => {
    if (roomRef.current && isRecording) {
      try {
        // Stop recording
        await stopRecording(roomRef.current);

        // Clear timer
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }

        // Disconnect from room
        disconnectFromRoom(roomRef.current);

        // Reset state
        setDuration(0);
        setIsRecording(false);

        // In a real implementation, we would get the recording URL from the server
        // For example:
        // const { recordingUrl } = await livekitApi.stopRecording(roomRef.current.name);
        // onRecordingComplete(recordingUrl);

        // For now, we'll use a mock URL
        const mockRecordingUrl = `https://example.com/recordings/recording-${Date.now()}.mp4`;
        onRecordingComplete(mockRecordingUrl);

        roomRef.current = null;
      } catch (err) {
        console.error('Error stopping recording:', err);
        setError('Failed to stop recording');
      }
    }
  };

  return (
    <div className={cn('flex flex-col items-center gap-4', className)}>
      {error && (
        <div className="text-error text-sm mb-2">{error}</div>
      )}

      <div className="relative">
        <button
          onClick={isRecording ? stopRecordingHandler : startRecording}
          disabled={hasPermission === false || isConnecting}
          className={cn(
            'flex items-center justify-center rounded-full w-16 h-16 transition-all',
            isRecording
              ? 'bg-error hover:bg-error/90'
              : 'bg-primary hover:bg-primary-hover',
            (hasPermission === false || isConnecting) && 'opacity-50 cursor-not-allowed'
          )}
        >
          {isConnecting ? (
            <svg
              className="animate-spin h-6 w-6 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          ) : isRecording ? (
            <span className="w-4 h-4 bg-white rounded"></span>
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-6 w-6 text-white"
            >
              <circle cx="12" cy="12" r="10" />
              <circle cx="12" cy="12" r="3" />
            </svg>
          )}
        </button>

        {isRecording && (
          <span className="absolute -top-2 -right-2 flex h-5 w-5 items-center justify-center">
            <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-error opacity-75"></span>
            <span className="relative inline-flex rounded-full h-3 w-3 bg-error"></span>
          </span>
        )}
      </div>

      {isRecording && (
        <div className="text-sm font-medium">
          {formatTime(duration)} / {formatTime(maxDuration)}
        </div>
      )}

      {!isRecording && !isConnecting && (
        <div className="text-sm text-foreground/70">
          {hasPermission === false
            ? 'Camera access required'
            : 'Click to start recording'}
        </div>
      )}

      {isConnecting && (
        <div className="text-sm text-foreground/70">
          Connecting to recording server...
        </div>
      )}
    </div>
  );
}
