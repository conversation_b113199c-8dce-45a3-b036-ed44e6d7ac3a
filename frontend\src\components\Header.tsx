'use client';

import Link from 'next/link';
import { cn } from '@/lib/utils';
import { useUser, UserButton, SignInButton } from '@clerk/nextjs';

interface HeaderProps {
  className?: string;
}

export function Header({ className }: HeaderProps) {
  const { isSignedIn, user } = useUser();

  return (
    <header className={cn('w-full border-b border-foreground/10 bg-background', className)}>
      <div className="container mx-auto flex h-16 items-center justify-between px-4">
        <Link href="/" className="flex items-center gap-2">
          <div className="relative flex h-8 w-8 items-center justify-center rounded-md bg-primary text-white">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-5 w-5"
            >
              <path d="M12 2a10 10 0 1 0 10 10H12V2z" />
              <path d="M12 12 2.1 12.5" />
              <path d="m17 17-5-5" />
            </svg>
          </div>
          <span className="text-xl font-semibold font-heading">EchoPilot</span>
        </Link>

        <nav className="flex items-center gap-6">
          {isSignedIn ? (
            <>
              <Link href="/dashboard" className="text-sm font-medium hover:text-primary">
                Dashboard
              </Link>
              <Link href="/videos" className="text-sm font-medium hover:text-primary">
                Videos
              </Link>
              <Link href="/record" className="btn-primary">
                Record Video
              </Link>
              <UserButton afterSignOutUrl="/" />
            </>
          ) : (
            <>
              <Link href="/features" className="text-sm font-medium hover:text-primary">
                Features
              </Link>
              <Link href="/pricing" className="text-sm font-medium hover:text-primary">
                Pricing
              </Link>
              <SignInButton mode="modal">
                <button className="text-sm font-medium hover:text-primary">
                  Sign In
                </button>
              </SignInButton>
              <Link href="/sign-up" className="btn-primary">
                Get Started
              </Link>
            </>
          )}
        </nav>
      </div>
    </header>
  );
}
