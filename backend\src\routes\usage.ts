import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { verifyAuth } from '../middleware/auth';
import { getTenantUsage, getTenantLimits } from '../services/usage';

interface AuthenticatedRequest extends FastifyRequest {
  user?: {
    id: string;
    clerkId: string;
    email: string;
    name: string;
    tenantId: string;
  };
}

export default async function (fastify: FastifyInstance) {
  // Authentication middleware
  fastify.addHook('onRequest', verifyAuth);

  // Get usage statistics for the tenant
  fastify.get('/', async (request: AuthenticatedRequest, reply: FastifyReply) => {
    if (!request.user) {
      return reply.status(401).send({ error: 'Unauthorized' });
    }

    const { tenantId } = request.user;

    try {
      const usage = await getTenantUsage(tenantId);
      const limits = await getTenantLimits(tenantId);
      
      return reply.send({
        usage,
        limits,
      });
    } catch (error) {
      console.error('Error fetching tenant usage:', error);
      return reply.status(500).send({ error: 'Failed to fetch usage statistics' });
    }
  });
}
