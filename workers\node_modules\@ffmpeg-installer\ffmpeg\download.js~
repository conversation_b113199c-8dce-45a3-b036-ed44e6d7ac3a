import * as path from 'path';
import platforms from './lib/platforms';
import { load, download, verify, extract, getPlatformDirectory, copyToPlatformDirectory } from './lib/manifest';

async function main(filter) {
    for (const platform of platforms.filter((platform) => (filter || '').length > 0 ? platform.indexOf(filter) >= 0 : true)) {
        const platformDirectory = getPlatformDirectory(platform);

        const manifest = load(path.join(platformDirectory, 'manifest.jsonc'));

        const archivePath = await download(manifest, platform);

        verify(manifest, archivePath);

        const extractPath = await extract(archivePath);

        copyToPlatformDirectory(manifest, platform, extractPath);
    }
}

main(...process.argv.slice(2));
