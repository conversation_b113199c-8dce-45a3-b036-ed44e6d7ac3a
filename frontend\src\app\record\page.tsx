'use client';

import { useState } from 'react';
import { Header } from '@/components/Header';
import { RecordButton } from '@/components/RecordButton';
import { formatBytes } from '@/lib/utils';

export default function RecordPage() {
  const [recordingState, setRecordingState] = useState<'idle' | 'recorded' | 'uploading' | 'success' | 'error'>('idle');
  const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null);
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [shareUrl, setShareUrl] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  const handleRecordingComplete = (recordingUrl: string) => {
    setRecordingState('recorded');
    setVideoUrl(recordingUrl);
  };

  const handleUpload = async () => {
    if (!videoUrl) return;

    setRecordingState('uploading');

    try {
      // In a real implementation, we would create a video record in the database
      // and update the status when processing is complete

      // Simulate upload progress
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + 10;
          if (newProgress >= 100) {
            clearInterval(interval);

            // Simulate successful upload
            setTimeout(() => {
              setRecordingState('success');
              const videoId = Math.random().toString(36).substring(2, 10);
              setShareUrl(`https://echopilot.example.com/v/${videoId}`);
            }, 500);

            return 100;
          }
          return newProgress;
        });
      }, 500);
    } catch (error) {
      console.error('Error uploading video:', error);
      setRecordingState('error');
    }
  };

  const handleReset = () => {
    setRecordingState('idle');
    setRecordedBlob(null);
    setVideoUrl(null);
    setShareUrl(null);
    setUploadProgress(0);
  };

  const copyShareUrl = () => {
    if (shareUrl) {
      navigator.clipboard.writeText(shareUrl);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-3xl font-semibold mb-6 font-heading">Record a Video Message</h1>

          <div className="card p-8">
            {recordingState === 'idle' && (
              <div className="flex flex-col items-center">
                <p className="text-center mb-8 text-foreground/70">
                  Record a video message to share with your team or clients.
                  <br />
                  You can record up to 5 minutes of video.
                </p>

                <RecordButton
                  onRecordingComplete={handleRecordingComplete}
                  maxDuration={300} // 5 minutes
                  className="mb-4"
                />
              </div>
            )}

            {recordingState === 'recorded' && videoUrl && (
              <div className="flex flex-col">
                <h2 className="text-xl font-medium mb-4">Preview Your Recording</h2>

                <div className="aspect-video bg-black rounded-lg mb-4 flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-16 w-16 text-white/40"
                  >
                    <polygon points="23 7 16 12 23 17 23 7" />
                    <rect x="1" y="5" width="15" height="14" rx="2" ry="2" />
                  </svg>
                </div>

                <div className="text-sm text-foreground/70 mb-6">
                  Your video has been recorded and is ready to be uploaded.
                </div>

                <div className="flex gap-4">
                  <button
                    onClick={handleUpload}
                    className="btn-primary"
                  >
                    Upload Video
                  </button>

                  <button
                    onClick={handleReset}
                    className="btn-secondary"
                  >
                    Record Again
                  </button>
                </div>
              </div>
            )}

            {recordingState === 'uploading' && (
              <div className="flex flex-col items-center">
                <h2 className="text-xl font-medium mb-4">Uploading Your Video</h2>

                <div className="w-full bg-foreground/10 rounded-full h-2.5 mb-4">
                  <div
                    className="bg-primary h-2.5 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>

                <p className="text-sm text-foreground/70">
                  {uploadProgress}% complete
                </p>
              </div>
            )}

            {recordingState === 'success' && shareUrl && (
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-success/10 rounded-full flex items-center justify-center mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-8 w-8 text-success"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                    <polyline points="22 4 12 14.01 9 11.01" />
                  </svg>
                </div>

                <h2 className="text-xl font-medium mb-2">Video Uploaded Successfully!</h2>

                <p className="text-center mb-6 text-foreground/70">
                  Your video is now available to share. Copy the link below:
                </p>

                <div className="flex w-full mb-6">
                  <input
                    type="text"
                    value={shareUrl}
                    readOnly
                    className="flex-1 px-4 py-2 border border-foreground/10 rounded-l-lg bg-foreground/5"
                  />
                  <button
                    onClick={copyShareUrl}
                    className="bg-primary text-white px-4 py-2 rounded-r-lg hover:bg-primary-hover"
                  >
                    Copy
                  </button>
                </div>

                <div className="flex gap-4">
                  <a
                    href={shareUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn-primary"
                  >
                    View Video
                  </a>

                  <button
                    onClick={handleReset}
                    className="btn-secondary"
                  >
                    Record Another
                  </button>
                </div>
              </div>
            )}

            {recordingState === 'error' && (
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-error/10 rounded-full flex items-center justify-center mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-8 w-8 text-error"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <line x1="15" y1="9" x2="9" y2="15" />
                    <line x1="9" y1="9" x2="15" y2="15" />
                  </svg>
                </div>

                <h2 className="text-xl font-medium mb-2">Upload Failed</h2>

                <p className="text-center mb-6 text-foreground/70">
                  There was an error uploading your video. Please try again.
                </p>

                <div className="flex gap-4">
                  <button
                    onClick={handleUpload}
                    className="btn-primary"
                  >
                    Try Again
                  </button>

                  <button
                    onClick={handleReset}
                    className="btn-secondary"
                  >
                    Record Again
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
