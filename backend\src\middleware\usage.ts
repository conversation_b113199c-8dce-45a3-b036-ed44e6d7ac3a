import { FastifyRequest, FastifyReply } from 'fastify';
import { trackApiCall, checkApiLimit } from '../services/usage';

interface AuthenticatedRequest extends FastifyRequest {
  user?: {
    id: string;
    clerkId: string;
    email: string;
    name: string;
    tenantId: string;
  };
}

/**
 * Middleware to track API usage and enforce limits
 */
export async function trackUsage(request: AuthenticatedRequest, reply: FastifyReply) {
  try {
    // Skip tracking for certain endpoints
    const skipEndpoints = [
      '/api/health',
      '/api/metrics',
    ];
    
    if (skipEndpoints.includes(request.url)) {
      return;
    }
    
    // Skip if no authenticated user
    if (!request.user || !request.user.tenantId) {
      return;
    }
    
    const { tenantId } = request.user;
    const endpoint = request.url;
    
    // Check if tenant has exceeded API limit
    const apiLimit = await checkApiLimit(tenantId);
    
    if (!apiLimit.allowed) {
      return reply.status(429).send({
        error: 'API rate limit exceeded',
        limit: apiLimit.limit,
        used: apiLimit.used,
        reset: 'at midnight UTC',
      });
    }
    
    // Track the API call (don't await to avoid blocking the request)
    trackApiCall(tenantId, endpoint).catch(err => {
      console.error('Error tracking API call:', err);
    });
  } catch (error) {
    console.error('Error in usage tracking middleware:', error);
    // Continue processing the request even if tracking fails
  }
}
