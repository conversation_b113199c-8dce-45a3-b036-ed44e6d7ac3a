"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParentCommand = void 0;
var ParentCommand;
(function (ParentCommand) {
    ParentCommand[ParentCommand["Completed"] = 0] = "Completed";
    ParentCommand[ParentCommand["Error"] = 1] = "Error";
    ParentCommand[ParentCommand["Failed"] = 2] = "Failed";
    ParentCommand[ParentCommand["InitFailed"] = 3] = "InitFailed";
    ParentCommand[ParentCommand["InitCompleted"] = 4] = "InitCompleted";
    ParentCommand[ParentCommand["Log"] = 5] = "Log";
    ParentCommand[ParentCommand["MoveToDelayed"] = 6] = "MoveToDelayed";
    ParentCommand[ParentCommand["Progress"] = 7] = "Progress";
    ParentCommand[ParentCommand["Update"] = 8] = "Update";
    ParentCommand[ParentCommand["GetChildrenValues"] = 9] = "GetChildrenValues";
})(ParentCommand = exports.ParentCommand || (exports.ParentCommand = {}));
//# sourceMappingURL=parent-command.js.map