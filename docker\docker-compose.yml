version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: echopilot-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: echopilot
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Redis for BullMQ
  redis:
    image: redis:alpine
    container_name: echopilot-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5

  # LiveKit Server
  livekit:
    image: livekit/livekit-server:latest
    container_name: echopilot-livekit
    ports:
      - "7880:7880"
      - "7881:7881"
      - "7882:7882/udp"
    environment:
      - LIVEKIT_KEYS=devkey:devsecret
      - LIVEKIT_TURN_ENABLED=true
    volumes:
      - livekit_data:/data
    command: --dev --bind 0.0.0.0 --redis-host redis

volumes:
  postgres_data:
  redis_data:
  livekit_data:
