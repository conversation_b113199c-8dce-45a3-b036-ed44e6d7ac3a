'use client';

import { useState, useEffect } from 'react';
import { Header } from '@/components/Header';
import Link from 'next/link';
import { formatDate } from '@/lib/utils';

interface Integration {
  id: string;
  name: string;
  type: string;
  enabled: boolean;
  createdAt: string;
}

export default function IntegrationsPage() {
  const [integrations, setIntegrations] = useState<Integration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchIntegrations() {
      try {
        const response = await fetch('/api/integrations');
        
        if (!response.ok) {
          throw new Error('Failed to fetch integrations');
        }
        
        const data = await response.json();
        setIntegrations(data);
      } catch (error) {
        console.error('Error fetching integrations:', error);
        setError('Failed to load integrations');
      } finally {
        setLoading(false);
      }
    }
    
    fetchIntegrations();
  }, []);

  const getIntegrationIcon = (type: string) => {
    switch (type) {
      case 'slack':
        return (
          <svg viewBox="0 0 24 24" fill="currentColor" className="h-6 w-6 text-[#4A154B]">
            <path d="M5.042 15.165a2.528 2.528 0 0 1-2.52 2.523A2.528 2.528 0 0 1 0 15.165a2.527 2.527 0 0 1 2.522-2.52h2.52v2.52zM6.313 15.165a2.527 2.527 0 0 1 2.521-2.52 2.527 2.527 0 0 1 2.521 2.52v6.313A2.528 2.528 0 0 1 8.834 24a2.528 2.528 0 0 1-2.521-2.522v-6.313zM8.834 5.042a2.528 2.528 0 0 1-2.521-2.52A2.528 2.528 0 0 1 8.834 0a2.528 2.528 0 0 1 2.521 2.522v2.52H8.834zM8.834 6.313a2.528 2.528 0 0 1 2.521 2.521 2.528 2.528 0 0 1-2.521 2.521H2.522A2.528 2.528 0 0 1 0 8.834a2.528 2.528 0 0 1 2.522-2.521h6.312zM18.956 8.834a2.528 2.528 0 0 1 2.522-2.521A2.528 2.528 0 0 1 24 8.834a2.528 2.528 0 0 1-2.522 2.521h-2.522V8.834zM17.688 8.834a2.528 2.528 0 0 1-2.523 2.521 2.527 2.527 0 0 1-2.52-2.521V2.522A2.527 2.527 0 0 1 15.165 0a2.528 2.528 0 0 1 2.523 2.522v6.312zM15.165 18.956a2.528 2.528 0 0 1 2.523 2.522A2.528 2.528 0 0 1 15.165 24a2.527 2.527 0 0 1-2.52-2.522v-2.522h2.52zM15.165 17.688a2.527 2.527 0 0 1-2.52-2.523 2.526 2.526 0 0 1 2.52-2.52h6.313A2.527 2.527 0 0 1 24 15.165a2.528 2.528 0 0 1-2.522 2.523h-6.313z" />
          </svg>
        );
      case 'zendesk':
        return (
          <svg viewBox="0 0 24 24" fill="currentColor" className="h-6 w-6 text-[#03363D]">
            <path d="M12 0C5.372 0 0 5.372 0 12s5.372 12 12 12 12-5.372 12-12S18.628 0 12 0zM1.655 12.976c0-1.86 1.51-3.37 3.37-3.37 1.86 0 3.37 1.51 3.37 3.37 0 1.86-1.51 3.37-3.37 3.37-1.86 0-3.37-1.51-3.37-3.37zm8.478-3.37c0-1.86 1.51-3.37 3.37-3.37 1.86 0 3.37 1.51 3.37 3.37 0 1.86-1.51 3.37-3.37 3.37-1.86 0-3.37-1.51-3.37-3.37zm8.478 3.37c0-1.86 1.51-3.37 3.37-3.37 1.86 0 3.37 1.51 3.37 3.37 0 1.86-1.51 3.37-3.37 3.37-1.86 0-3.37-1.51-3.37-3.37zm-4.239 4.239c0 1.86-1.51 3.37-3.37 3.37-1.86 0-3.37-1.51-3.37-3.37 0-1.86 1.51-3.37 3.37-3.37 1.86 0 3.37 1.51 3.37 3.37z" />
          </svg>
        );
      case 'hubspot':
        return (
          <svg viewBox="0 0 24 24" fill="currentColor" className="h-6 w-6 text-[#FF7A59]">
            <path d="M22.447 9.602h-4.978V4.623a1.55 1.55 0 0 0-1.553-1.552 1.55 1.55 0 0 0-1.553 1.552v4.979h-4.979a1.55 1.55 0 0 0-1.552 1.553 1.55 1.55 0 0 0 1.552 1.553h4.979v4.978a1.55 1.55 0 0 0 1.553 1.553 1.55 1.55 0 0 0 1.553-1.553v-4.978h4.978a1.55 1.55 0 0 0 1.553-1.553 1.55 1.55 0 0 0-1.553-1.553zm-9.55 6.42v-.304c-.005-1.042-.847-1.884-1.889-1.889h-.304c-1.042.005-1.884.847-1.889 1.889v.304c.005 1.042.847 1.884 1.889 1.889h.304c1.042-.005 1.884-.847 1.889-1.889zm-6.42-6.42v-.304c-.005-1.042-.847-1.884-1.889-1.889H4.284c-1.042.005-1.884.847-1.889 1.889v.304c.005 1.042.847 1.884 1.889 1.889h.304c1.042-.005 1.884-.847 1.889-1.889zm6.42-6.419v-.304c-.005-1.042-.847-1.884-1.889-1.889h-.304c-1.042.005-1.884.847-1.889 1.889v.304c.005 1.042.847 1.884 1.889 1.889h.304c1.042-.005 1.884-.847 1.889-1.889z" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6 text-primary">
            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
          </svg>
        );
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-semibold font-heading">Integrations</h1>
          
          <Link href="/integrations/new" className="btn-primary">
            Add Integration
          </Link>
        </div>
        
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="card py-12 text-center">
            <h2 className="text-xl font-medium mb-4 text-error">Error</h2>
            <p className="text-foreground/70 mb-6">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="btn-primary inline-block"
            >
              Try Again
            </button>
          </div>
        ) : integrations.length === 0 ? (
          <div className="card py-12 text-center">
            <h2 className="text-xl font-medium mb-4">No Integrations Yet</h2>
            <p className="text-foreground/70 mb-6">
              You haven't set up any integrations yet. Click the button below to get started.
            </p>
            <Link href="/integrations/new" className="btn-primary inline-block">
              Add Your First Integration
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {integrations.map((integration) => (
              <Link
                key={integration.id}
                href={`/integrations/${integration.id}`}
                className="card hover:shadow-md transition-shadow"
              >
                <div className="flex items-center gap-4 mb-4">
                  <div className="flex-shrink-0">
                    {getIntegrationIcon(integration.type)}
                  </div>
                  <div>
                    <h3 className="text-lg font-medium">{integration.name}</h3>
                    <p className="text-sm text-foreground/70 capitalize">{integration.type}</p>
                  </div>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-foreground/70">
                    Added {formatDate(integration.createdAt)}
                  </span>
                  <span className={`px-2 py-0.5 rounded-full text-xs ${
                    integration.enabled
                      ? 'bg-success/10 text-success'
                      : 'bg-foreground/10 text-foreground/70'
                  }`}>
                    {integration.enabled ? 'Enabled' : 'Disabled'}
                  </span>
                </div>
              </Link>
            ))}
          </div>
        )}
        
        <div className="mt-12">
          <h2 className="text-2xl font-semibold font-heading mb-6">Available Integrations</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="card hover:shadow-md transition-shadow">
              <div className="flex items-center gap-4 mb-4">
                <div className="flex-shrink-0">
                  {getIntegrationIcon('slack')}
                </div>
                <div>
                  <h3 className="text-lg font-medium">Slack</h3>
                  <p className="text-sm text-foreground/70">Send video notifications to Slack channels</p>
                </div>
              </div>
              
              <Link href="/integrations/new?type=slack" className="btn-secondary w-full text-center">
                Connect Slack
              </Link>
            </div>
            
            <div className="card hover:shadow-md transition-shadow">
              <div className="flex items-center gap-4 mb-4">
                <div className="flex-shrink-0">
                  {getIntegrationIcon('zendesk')}
                </div>
                <div>
                  <h3 className="text-lg font-medium">Zendesk</h3>
                  <p className="text-sm text-foreground/70">Create tickets from videos</p>
                </div>
              </div>
              
              <Link href="/integrations/new?type=zendesk" className="btn-secondary w-full text-center">
                Connect Zendesk
              </Link>
            </div>
            
            <div className="card hover:shadow-md transition-shadow">
              <div className="flex items-center gap-4 mb-4">
                <div className="flex-shrink-0">
                  {getIntegrationIcon('hubspot')}
                </div>
                <div>
                  <h3 className="text-lg font-medium">HubSpot</h3>
                  <p className="text-sm text-foreground/70">Create notes and tasks in HubSpot</p>
                </div>
              </div>
              
              <Link href="/integrations/new?type=hubspot" className="btn-secondary w-full text-center">
                Connect HubSpot
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
