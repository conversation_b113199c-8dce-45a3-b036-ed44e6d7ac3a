import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Define plan limits
const PLAN_LIMITS = {
  free: {
    storageGB: 5,
    videosPerMonth: 50,
    maxVideoDurationMin: 30,
    maxVideoSizeMB: 500,
    apiCallsPerDay: 100,
  },
  pro: {
    storageGB: 50,
    videosPerMonth: 500,
    maxVideoDurationMin: 120,
    maxVideoSizeMB: 2000,
    apiCallsPerDay: 1000,
  },
  enterprise: {
    storageGB: 500,
    videosPerMonth: 5000,
    maxVideoDurationMin: 240,
    maxVideoSizeMB: 5000,
    apiCallsPerDay: 10000,
  },
};

/**
 * Get the limits for a tenant's plan
 */
export async function getTenantLimits(tenantId: string): Promise<typeof PLAN_LIMITS.free> {
  try {
    const tenant = await prisma.tenant.findUnique({
      where: { id: tenantId },
    });
    
    if (!tenant) {
      throw new Error(`Tenant ${tenantId} not found`);
    }
    
    const plan = tenant.plan as keyof typeof PLAN_LIMITS;
    
    return PLAN_LIMITS[plan] || PLAN_LIMITS.free;
  } catch (error) {
    console.error(`Error getting tenant limits for ${tenantId}:`, error);
    return PLAN_LIMITS.free; // Default to free plan limits
  }
}

/**
 * Check if a tenant has exceeded their storage limit
 */
export async function checkStorageLimit(tenantId: string): Promise<{ allowed: boolean; used: number; limit: number }> {
  try {
    // Get tenant's plan limits
    const limits = await getTenantLimits(tenantId);
    
    // Calculate total storage used (in MB)
    const videos = await prisma.video.findMany({
      where: { tenantId },
      select: { sizeMb: true },
    });
    
    const totalStorageMB = videos.reduce((total, video) => total + (video.sizeMb || 0), 0);
    const totalStorageGB = totalStorageMB / 1024;
    
    return {
      allowed: totalStorageGB < limits.storageGB,
      used: totalStorageGB,
      limit: limits.storageGB,
    };
  } catch (error) {
    console.error(`Error checking storage limit for ${tenantId}:`, error);
    return { allowed: false, used: 0, limit: 0 };
  }
}

/**
 * Check if a tenant has exceeded their monthly video limit
 */
export async function checkVideoLimit(tenantId: string): Promise<{ allowed: boolean; used: number; limit: number }> {
  try {
    // Get tenant's plan limits
    const limits = await getTenantLimits(tenantId);
    
    // Calculate videos created this month
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);
    
    const videosThisMonth = await prisma.video.count({
      where: {
        tenantId,
        createdAt: {
          gte: startOfMonth,
        },
      },
    });
    
    return {
      allowed: videosThisMonth < limits.videosPerMonth,
      used: videosThisMonth,
      limit: limits.videosPerMonth,
    };
  } catch (error) {
    console.error(`Error checking video limit for ${tenantId}:`, error);
    return { allowed: false, used: 0, limit: 0 };
  }
}

/**
 * Check if a video exceeds the tenant's size or duration limits
 */
export async function checkVideoSizeAndDuration(
  tenantId: string,
  sizeMb: number,
  durationSec: number
): Promise<{ allowed: boolean; sizeLimit: number; durationLimit: number }> {
  try {
    // Get tenant's plan limits
    const limits = await getTenantLimits(tenantId);
    
    const durationMin = durationSec / 60;
    
    return {
      allowed: sizeMb <= limits.maxVideoSizeMB && durationMin <= limits.maxVideoDurationMin,
      sizeLimit: limits.maxVideoSizeMB,
      durationLimit: limits.maxVideoDurationMin,
    };
  } catch (error) {
    console.error(`Error checking video size and duration for ${tenantId}:`, error);
    return { allowed: false, sizeLimit: 0, durationLimit: 0 };
  }
}

/**
 * Track an API call for a tenant
 */
export async function trackApiCall(tenantId: string, endpoint: string): Promise<void> {
  try {
    // Get the current date (without time)
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Find or create a usage record for today
    const usage = await prisma.apiUsage.findFirst({
      where: {
        tenantId,
        date: today,
      },
    });
    
    if (usage) {
      // Update existing usage record
      await prisma.apiUsage.update({
        where: { id: usage.id },
        data: {
          count: usage.count + 1,
          endpoints: {
            ...usage.endpoints,
            [endpoint]: (usage.endpoints[endpoint] || 0) + 1,
          },
        },
      });
    } else {
      // Create new usage record
      await prisma.apiUsage.create({
        data: {
          tenantId,
          date: today,
          count: 1,
          endpoints: {
            [endpoint]: 1,
          },
        },
      });
    }
  } catch (error) {
    console.error(`Error tracking API call for ${tenantId}:`, error);
  }
}

/**
 * Check if a tenant has exceeded their daily API call limit
 */
export async function checkApiLimit(tenantId: string): Promise<{ allowed: boolean; used: number; limit: number }> {
  try {
    // Get tenant's plan limits
    const limits = await getTenantLimits(tenantId);
    
    // Get today's date (without time)
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Get today's API usage
    const usage = await prisma.apiUsage.findFirst({
      where: {
        tenantId,
        date: today,
      },
    });
    
    const apiCallsToday = usage?.count || 0;
    
    return {
      allowed: apiCallsToday < limits.apiCallsPerDay,
      used: apiCallsToday,
      limit: limits.apiCallsPerDay,
    };
  } catch (error) {
    console.error(`Error checking API limit for ${tenantId}:`, error);
    return { allowed: false, used: 0, limit: 0 };
  }
}

/**
 * Get usage statistics for a tenant
 */
export async function getTenantUsage(tenantId: string): Promise<{
  storage: { used: number; limit: number; percentage: number };
  videos: { used: number; limit: number; percentage: number };
  apiCalls: { used: number; limit: number; percentage: number };
}> {
  try {
    const storageCheck = await checkStorageLimit(tenantId);
    const videoCheck = await checkVideoLimit(tenantId);
    const apiCheck = await checkApiLimit(tenantId);
    
    return {
      storage: {
        used: storageCheck.used,
        limit: storageCheck.limit,
        percentage: (storageCheck.used / storageCheck.limit) * 100,
      },
      videos: {
        used: videoCheck.used,
        limit: videoCheck.limit,
        percentage: (videoCheck.used / videoCheck.limit) * 100,
      },
      apiCalls: {
        used: apiCheck.used,
        limit: apiCheck.limit,
        percentage: (apiCheck.used / apiCheck.limit) * 100,
      },
    };
  } catch (error) {
    console.error(`Error getting tenant usage for ${tenantId}:`, error);
    throw error;
  }
}

export default {
  getTenantLimits,
  checkStorageLimit,
  checkVideoLimit,
  checkVideoSizeAndDuration,
  trackApiCall,
  checkApiLimit,
  getTenantUsage,
};
