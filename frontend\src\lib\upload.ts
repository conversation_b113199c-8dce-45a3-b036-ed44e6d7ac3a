'use client';

import * as tus from 'tus-js-client';

// Upload endpoint
const UPLOAD_ENDPOINT = process.env.NEXT_PUBLIC_UPLOAD_URL || 'http://localhost:3001/uploads';

interface UploadOptions {
  file: File | Blob;
  metadata?: Record<string, string>;
  onProgress?: (progress: number) => void;
  onSuccess?: (url: string) => void;
  onError?: (error: Error) => void;
}

/**
 * Upload a file using tus resumable upload protocol
 */
export function uploadFile({
  file,
  metadata = {},
  onProgress,
  onSuccess,
  onError,
}: UploadOptions): tus.Upload {
  // Create a new tus upload
  const upload = new tus.Upload(file, {
    endpoint: UPLOAD_ENDPOINT,
    retryDelays: [0, 1000, 3000, 5000],
    metadata: {
      filename: metadata.filename || 'video.webm',
      filetype: metadata.filetype || 'video/webm',
      ...metadata,
    },
    onError: (error) => {
      console.error('Upload error:', error);
      if (onError) onError(error);
    },
    onProgress: (bytesUploaded, bytesTotal) => {
      const percentage = (bytesUploaded / bytesTotal) * 100;
      if (onProgress) onProgress(percentage);
    },
    onSuccess: () => {
      if (upload.url && onSuccess) {
        // The URL includes the endpoint and the file ID
        onSuccess(upload.url);
      }
    },
  });

  // Start the upload
  upload.start();

  return upload;
}

/**
 * Convert a Blob to a File object
 */
export function blobToFile(blob: Blob, filename: string): File {
  return new File([blob], filename, { type: blob.type });
}

/**
 * Upload a video recording
 */
export function uploadVideo(
  videoBlob: Blob,
  metadata: {
    tenantId: string;
    uploaderId: string;
    filename?: string;
  },
  callbacks: {
    onProgress?: (progress: number) => void;
    onSuccess?: (url: string) => void;
    onError?: (error: Error) => void;
  }
): tus.Upload {
  const filename = metadata.filename || `video-${Date.now()}.webm`;
  const file = blobToFile(videoBlob, filename);

  return uploadFile({
    file,
    metadata: {
      filename,
      filetype: 'video/webm',
      tenantId: metadata.tenantId,
      uploaderId: metadata.uploaderId,
    },
    onProgress: callbacks.onProgress,
    onSuccess: callbacks.onSuccess,
    onError: callbacks.onError,
  });
}
