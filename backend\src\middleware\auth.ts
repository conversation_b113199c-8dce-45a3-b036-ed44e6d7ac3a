import { FastifyRequest, FastifyReply } from 'fastify';
import { clerkClient, verifyToken } from '@clerk/backend';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface AuthenticatedRequest extends FastifyRequest {
  user?: {
    id: string;
    clerkId: string;
    email: string;
    name: string;
    tenantId: string;
  };
}

/**
 * Middleware to verify Clerk authentication token
 */
export async function verifyAuth(request: AuthenticatedRequest, reply: FastifyReply) {
  try {
    // Skip authentication for development if needed
    if (process.env.NODE_ENV === 'development' && process.env.SKIP_AUTH === 'true') {
      // For development, you can set a mock user
      request.user = {
        id: 'mock-user-id',
        clerkId: 'mock-clerk-id',
        email: '<EMAIL>',
        name: 'Dev User',
        tenantId: 'mock-tenant-id',
      };
      return;
    }

    // Get the token from the Authorization header
    const authHeader = request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return reply.status(401).send({ error: 'Unauthorized: Missing or invalid token' });
    }

    const token = authHeader.split(' ')[1];
    
    // Verify the token
    const decodedToken = await verifyToken(token);
    if (!decodedToken) {
      return reply.status(401).send({ error: 'Unauthorized: Invalid token' });
    }

    // Get user details from Clerk
    const clerkUser = await clerkClient.users.getUser(decodedToken.sub);
    if (!clerkUser) {
      return reply.status(401).send({ error: 'Unauthorized: User not found' });
    }

    // Get the primary email
    const primaryEmail = clerkUser.emailAddresses.find(
      email => email.id === clerkUser.primaryEmailAddressId
    );

    if (!primaryEmail) {
      return reply.status(401).send({ error: 'Unauthorized: User has no email' });
    }

    // Find or create user in our database
    let user = await prisma.user.findUnique({
      where: { clerkId: clerkUser.id },
    });

    if (!user) {
      // Create a new tenant for the user
      const tenant = await prisma.tenant.create({
        data: {
          name: `${clerkUser.firstName}'s Workspace`,
          plan: 'free',
        },
      });

      // Create the user
      user = await prisma.user.create({
        data: {
          clerkId: clerkUser.id,
          email: primaryEmail.emailAddress,
          name: `${clerkUser.firstName} ${clerkUser.lastName}`,
          tenantId: tenant.id,
          role: 'admin',
        },
      });
    }

    // Add user to request
    request.user = {
      id: user.id,
      clerkId: user.clerkId || '',
      email: user.email,
      name: user.name,
      tenantId: user.tenantId,
    };
  } catch (error) {
    console.error('Authentication error:', error);
    return reply.status(401).send({ error: 'Unauthorized: Authentication failed' });
  }
}
