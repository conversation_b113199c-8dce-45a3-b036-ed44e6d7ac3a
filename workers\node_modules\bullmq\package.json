{"name": "bullmq", "version": "5.52.3", "description": "Queue for messages and jobs based on Redis", "homepage": "https://bullmq.io/", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/esm/index.d.ts", "source": "./src/index.ts", "author": "Taskforce.sh Inc.", "license": "MIT", "keywords": ["bull", "bullmq", "queues", "jobs", "redis"], "files": ["dist"], "scripts": {"build": "npm-run-all pretest tsc:all copy:main:type clean:temp:files copy:lua copy:includes:lua", "circular:references": "madge --circular --extensions ts ./src", "clean:scripts": "rimraf rawScripts src/scripts", "clean:temp:files": "rimraf dist/cjs/bullmq.d.ts dist/esm/bullmq.d.ts dist/tsdoc-metadata.json", "copy:includes:lua": "copyfiles -f ./src/commands/includes/*.lua ./dist/cjs/commands/includes && copyfiles -f ./src/commands/includes/*.lua ./dist/esm/commands/includes", "copy:lua": "copyfiles -f ./src/commands/*.lua ./dist/cjs/commands && copyfiles -f ./src/commands/*.lua ./dist/esm/commands", "copy:lua:python": "copyfiles -f ./rawScripts/*.lua ./python/bullmq/commands", "copy:main:type": "copyfiles -f ./dist/esm/classes/main.d.ts ./dist/esm/classes/main-worker.d.ts ./dist/cjs/classes", "coverage": "nyc --reporter=text --reporter=lcovonly npm run test", "cm": "git cz", "docs": "typedoc --excludeExternals --excludeProtected --excludePrivate --readme none src/index.ts", "docs:json": "typedoc --excludeExternals --excludeProtected --excludePrivate --readme none src/index.ts --json ./apiVersions/v5.json --name v5", "dc:up": "docker-compose -f docker-compose.yml up -d", "dc:down": "docker-compose -f docker-compose.yml down", "dry-run": "npm publish --dry-run", "eslint:fix": "./node_modules/.bin/eslint . --fix", "generate:raw:scripts": "ts-node --project tsconfig-cjs.json scripts/generateRawScripts.ts", "lint": "./node_modules/.bin/eslint .", "lint:staged": "lint-staged", "prepublishOnly": "npm run build", "prepare": "husky install", "pretest": "npm-run-all clean:scripts generate:raw:scripts transform:commands", "prettier": "prettier --config package.json src/**/*.ts", "pretty:quick": "pretty-quick --staged", "semantic-release": "semantic-release", "semantic-release-prepare": "ts-node tools/semantic-release-prepare", "test": "NODE_ENV=test ts-mocha -p tsconfig-cjs.json --config ./.mocharc.js", "test:watch": "ts-mocha -p tsconfig-cjs.json --paths 'tests/test_*.ts' -w --watch-extensions ts", "transform:commands": "node ./scripts/commandTransform.js ./rawScripts ./src/scripts", "tsc": "tsc", "tsc:all": "tsc && tsc -p tsconfig-cjs.json"}, "dependencies": {"cron-parser": "^4.9.0", "ioredis": "^5.4.1", "msgpackr": "^1.11.2", "node-abort-controller": "^3.1.1", "semver": "^7.5.4", "tslib": "^2.0.0", "uuid": "^9.0.0"}, "devDependencies": {"@commitlint/cli": "^17.0.3", "@commitlint/config-conventional": "^17.0.3", "@istanbuljs/nyc-config-typescript": "^1.0.2", "@semantic-release/changelog": "^6.0.1", "@semantic-release/commit-analyzer": "^13.0.1", "@semantic-release/exec": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.1", "@semantic-release/npm": "^9.0.1", "@semantic-release/release-notes-generator": "^10.0.3", "@types/chai": "^4.3.1", "@types/chai-as-promised": "^7.1.5", "@types/lodash.defaults": "^4.2.7", "@types/lodash.isarguments": "^3.1.7", "@types/mocha": "^5.2.7", "@types/msgpack": "^0.0.31", "@types/node": "^12.20.25", "@types/semver": "^7.3.9", "@types/sinon": "^10.0.13", "@types/uuid": "^3.4.10", "@typescript-eslint/eslint-plugin": "^8.28.0", "@typescript-eslint/parser": "^8.28.0", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "commitizen": "^4.2.5", "copyfiles": "^2.4.1", "coveralls-next": "^4.2.1", "eslint": "^9.24.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-mocha": "^10.1.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-tsdoc": "^0.4.0", "fast-glob": "^3.3.3", "husky": "^8.0.1", "istanbul": "^0.4.5", "lint-staged": "^15.5.0", "madge": "^8.0.0", "minimatch": "^9.0.3", "mocha": "^11.1.0", "mocha-lcov-reporter": "^1.3.0", "moment": "^2.29.4", "npm-run-all": "^4.1.5", "nyc": "^17.1.0", "prettier": "^2.7.1", "pretty-quick": "^3.1.3", "progress": "^2.0.3", "rimraf": "^3.0.2", "rrule": "^2.6.9", "semantic-release": "^24.2.3", "sinon": "^18.0.1", "test-console": "^2.0.0", "ts-mocha": "^11.1.0", "ts-node": "^10.7.0", "typedoc": "^0.28.2", "typescript": "^4.7.4"}, "resolutions": {"cross-spawn": "7.0.6", "micromatch": "4.0.8", "tar": "7.4.3"}, "nyc": {"extends": "@istanbuljs/nyc-config-typescript", "all": true, "check-coverage": true, "include": ["src/**/*.ts"], "exclude": ["bullmq-tests/test_*.ts"], "lines": 80, "functions": 80, "branches": 70, "statements": 80, "watermarks": {"lines": [80, 95], "functions": [80, 95], "branches": [80, 95], "statements": [80, 95]}}, "config": {"commitizen": {"path": "node_modules/cz-conventional-changelog"}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"*.{js,ts}": "npm run eslint:fix"}, "repository": {"type": "git", "url": "https://github.com/taskforcesh/bullmq.git"}, "release": {"plugins": [["@semantic-release/commit-analyzer", {"releaseRules": [{"message": "*\\[python\\]*", "release": false}]}], "@semantic-release/release-notes-generator", ["@semantic-release/changelog", {"changelogFile": "docs/gitbook/changelog.md"}], ["@semantic-release/exec", {"prepareCmd": "node scripts/updateVersion.js ${nextRelease.version} && yarn build"}], ["@semantic-release/npm", {"npmPublish": true}], "@semantic-release/github", ["@semantic-release/git", {"assets": ["package.json", "yarn.lock", "src/version.ts", "docs/gitbook/changelog.md", "docs/gitbook/api/**"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}]]}}