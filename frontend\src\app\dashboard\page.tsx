'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Header } from '@/components/Header';
import { formatDate, formatTime } from '@/lib/utils';

interface Video {
  id: string;
  title?: string;
  status: string;
  thumbnailUrl?: string;
  durationSec?: number;
  createdAt: string;
  views: number;
}

export default function DashboardPage() {
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchVideos() {
      try {
        const response = await fetch('/api/videos');

        if (!response.ok) {
          throw new Error('Failed to fetch videos');
        }

        const data = await response.json();
        setVideos(data);
      } catch (error) {
        console.error('Error fetching videos:', error);
        // If there's an error, use mock data for demonstration
        setVideos([
          {
            id: '1',
            title: 'Product Demo',
            status: 'ready',
            thumbnailUrl: '/thumbnail1.jpg',
            durationSec: 125,
            createdAt: new Date().toISOString(),
            views: 12,
          },
          {
            id: '2',
            title: 'Team Update',
            status: 'ready',
            thumbnailUrl: '/thumbnail2.jpg',
            durationSec: 245,
            createdAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
            views: 8,
          },
          {
            id: '3',
            title: 'Feature Walkthrough',
            status: 'processing',
            createdAt: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
            views: 0,
          },
        ]);
      } finally {
        setLoading(false);
      }
    }

    fetchVideos();
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-semibold font-heading">Your Videos</h1>

          <Link href="/record" className="btn-primary">
            Record New Video
          </Link>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : videos.length === 0 ? (
          <div className="card py-12 text-center">
            <h2 className="text-xl font-medium mb-4">No Videos Yet</h2>
            <p className="text-foreground/70 mb-6">
              You haven't recorded any videos yet. Click the button below to get started.
            </p>
            <Link href="/record" className="btn-primary inline-block">
              Record Your First Video
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {videos.map((video) => (
              <div key={video.id} className="card overflow-hidden">
                <div className="aspect-video bg-foreground/5 relative mb-4">
                  {video.thumbnailUrl ? (
                    <div
                      className="w-full h-full bg-cover bg-center"
                      style={{ backgroundImage: `url(${video.thumbnailUrl})` }}
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-12 w-12 text-foreground/20"
                      >
                        <polygon points="23 7 16 12 23 17 23 7" />
                        <rect x="1" y="5" width="15" height="14" rx="2" ry="2" />
                      </svg>
                    </div>
                  )}

                  {video.status === 'processing' && (
                    <div className="absolute inset-0 bg-foreground/50 flex items-center justify-center">
                      <div className="bg-white dark:bg-foreground/90 rounded-lg px-4 py-2 flex items-center">
                        <svg
                          className="animate-spin h-5 w-5 mr-2 text-primary"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        <span className="text-sm font-medium">Processing</span>
                      </div>
                    </div>
                  )}

                  {video.durationSec && (
                    <div className="absolute bottom-2 right-2 bg-foreground/75 text-white text-xs px-2 py-1 rounded">
                      {formatTime(video.durationSec)}
                    </div>
                  )}
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-1">
                    {video.title || 'Untitled Video'}
                  </h3>

                  <div className="flex items-center justify-between text-sm text-foreground/70 mb-4">
                    <span>{formatDate(video.createdAt)}</span>
                    <span>{video.views} {video.views === 1 ? 'view' : 'views'}</span>
                  </div>

                  <div className="flex gap-2">
                    <Link
                      href={`/v/${video.id}`}
                      className="btn-secondary text-sm py-1 px-3 flex-1 text-center"
                    >
                      View
                    </Link>

                    <button
                      className="btn-secondary text-sm py-1 px-3"
                      onClick={() => {
                        navigator.clipboard.writeText(`https://echopilot.example.com/v/${video.id}`);
                      }}
                    >
                      Copy Link
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </main>
    </div>
  );
}
