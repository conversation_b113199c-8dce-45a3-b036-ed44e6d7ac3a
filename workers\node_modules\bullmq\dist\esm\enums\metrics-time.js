export var MetricsTime;
(function (MetricsTime) {
    MetricsTime[MetricsTime["ONE_MINUTE"] = 1] = "ONE_MINUTE";
    MetricsTime[MetricsTime["FIVE_MINUTES"] = 5] = "FIVE_MINUTES";
    MetricsTime[MetricsTime["FIFTEEN_MINUTES"] = 15] = "FIFTEEN_MINUTES";
    MetricsTime[MetricsTime["THIRTY_MINUTES"] = 30] = "THIRTY_MINUTES";
    MetricsTime[MetricsTime["ONE_HOUR"] = 60] = "ONE_HOUR";
    MetricsTime[MetricsTime["ONE_WEEK"] = 10080] = "ONE_WEEK";
    MetricsTime[MetricsTime["TWO_WEEKS"] = 20160] = "TWO_WEEKS";
    MetricsTime[MetricsTime["ONE_MONTH"] = 80640] = "ONE_MONTH";
})(MetricsTime || (MetricsTime = {}));
//# sourceMappingURL=metrics-time.js.map