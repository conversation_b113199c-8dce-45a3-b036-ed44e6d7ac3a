import Link from 'next/link';
import { Header } from '@/components/Header';

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 flex flex-col">
        {/* Hero Section */}
        <section className="py-20 px-4">
          <div className="container mx-auto max-w-6xl">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
              <div>
                <h1 className="text-4xl md:text-5xl font-semibold mb-6 font-heading">
                  Video Messaging with <span className="text-primary">AI-Powered</span> Insights
                </h1>
                <p className="text-lg mb-8 text-foreground/80">
                  Record, share, and analyze video messages with automatic transcription,
                  sentiment analysis, and smart summaries.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Link href="/record" className="btn-primary text-center py-3 px-6">
                    Record a Video
                  </Link>
                  <Link href="/dashboard" className="btn-secondary text-center py-3 px-6">
                    View Dashboard
                  </Link>
                </div>
              </div>

              <div className="relative">
                <div className="aspect-video bg-foreground/5 rounded-2xl overflow-hidden flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-16 w-16 text-primary/40"
                  >
                    <polygon points="23 7 16 12 23 17 23 7" />
                    <rect x="1" y="5" width="15" height="14" rx="2" ry="2" />
                  </svg>
                </div>

                <div className="absolute -bottom-6 -right-6 bg-white dark:bg-foreground/10 p-4 rounded-xl shadow-lg">
                  <div className="flex items-center gap-3">
                    <div className="kpi-chip kpi-chip-success">Positive Tone</div>
                    <div className="text-sm font-medium">98% Accuracy</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 px-4 bg-foreground/5">
          <div className="container mx-auto max-w-6xl">
            <h2 className="text-3xl font-semibold mb-12 text-center font-heading">Key Features</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="card">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-6 w-6 text-primary"
                  >
                    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z" />
                    <circle cx="12" cy="13" r="4" />
                  </svg>
                </div>
                <h3 className="text-xl font-medium mb-2 font-heading">Easy Recording</h3>
                <p className="text-foreground/70">
                  Record videos directly in your browser with no additional software required.
                </p>
              </div>

              <div className="card">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-6 w-6 text-primary"
                  >
                    <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3" />
                  </svg>
                </div>
                <h3 className="text-xl font-medium mb-2 font-heading">AI Insights</h3>
                <p className="text-foreground/70">
                  Automatic transcription, sentiment analysis, and smart summaries for every video.
                </p>
              </div>

              <div className="card">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-6 w-6 text-primary"
                  >
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
                    <circle cx="9" cy="7" r="4" />
                    <path d="M23 21v-2a4 4 0 0 0-3-3.87" />
                    <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                  </svg>
                </div>
                <h3 className="text-xl font-medium mb-2 font-heading">Easy Sharing</h3>
                <p className="text-foreground/70">
                  Share videos with anyone via a simple link, no account required to view.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-4">
          <div className="container mx-auto max-w-3xl text-center">
            <h2 className="text-3xl font-semibold mb-4 font-heading">Ready to get started?</h2>
            <p className="text-lg mb-8 text-foreground/80">
              Record your first video message in seconds, no credit card required.
            </p>
            <Link href="/record" className="btn-primary inline-block py-3 px-8">
              Try It Now
            </Link>
          </div>
        </section>
      </main>

      <footer className="border-t border-foreground/10 py-8 px-4">
        <div className="container mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <div className="flex items-center gap-2">
                <div className="relative flex h-8 w-8 items-center justify-center rounded-md bg-primary text-white">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-5 w-5"
                  >
                    <path d="M12 2a10 10 0 1 0 10 10H12V2z" />
                    <path d="M12 12 2.1 12.5" />
                    <path d="m17 17-5-5" />
                  </svg>
                </div>
                <span className="text-xl font-semibold font-heading">EchoPilot</span>
              </div>
            </div>

            <div className="text-sm text-foreground/70">
              &copy; {new Date().getFullYear()} EchoPilot. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
