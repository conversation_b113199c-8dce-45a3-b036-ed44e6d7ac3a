--[[
  Function to store a job
]]
local function storeJob(events<PERSON>ey, jobIdKey, jobId, name, data, opts, timestamp,
                        parentKey, parentData, repeatJob<PERSON>ey)
    local jsonOpts = cjson.encode(opts)
    local delay = opts['delay'] or 0
    local priority = opts['priority'] or 0
    local debounceId = opts['de'] and opts['de']['id']
    
    local optionalValues = {}
    if parentKey ~= nil then
        table.insert(optionalValues, "parentKey")
        table.insert(optionalValues, parentKey)
        table.insert(optionalValues, "parent")
        table.insert(optionalValues, parentData)
    end

    if repeatJob<PERSON>ey then
        table.insert(optionalValues, "rjk")
        table.insert(optionalValues, repeatJob<PERSON>ey)
    end

    if debounceId then
        table.insert(optionalValues, "deid")
        table.insert(optionalValues, debounceId)
    end

    rcall("HMSET", jobIdKey, "name", name, "data", data, "opts", jsonOpts,
          "timestamp", timestamp, "delay", delay, "priority", priority,
          unpack(optionalValues))

    rcall("XADD", eventsKey, "*", "event", "added", "jobId", jobId, "name", name)

    return delay, priority
end
