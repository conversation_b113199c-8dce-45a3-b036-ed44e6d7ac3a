import { FastifyInstance } from 'fastify';
import { Server } from 'tus-node-server';
import { FileStore } from 'tus-node-server/lib/stores/FileStore';
import { PrismaClient } from '@prisma/client';
import path from 'path';
import fs from 'fs';
import { queueVideoProcessing } from '../services/queue';

const prisma = new PrismaClient();

// Create uploads directory if it doesn't exist
const UPLOAD_PATH = path.resolve(process.cwd(), 'uploads');
if (!fs.existsSync(UPLOAD_PATH)) {
  fs.mkdirSync(UPLOAD_PATH, { recursive: true });
}

// Configure tus server
const tusServer = new Server();
tusServer.datastore = new FileStore({
  path: '/uploads',
  directory: UPLOAD_PATH,
});

export default async function (fastify: FastifyInstance) {
  // Handle tus protocol requests
  fastify.all('/uploads/*', async (request, reply) => {
    // Skip authentication for development
    if (process.env.NODE_ENV !== 'development') {
      try {
        await request.jwtVerify();
      } catch (err) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }
    }

    // Handle the tus request
    return new Promise((resolve) => {
      tusServer.handle(request.raw, reply.raw, () => {
        // This callback is called when tus-node-server has handled the request
        resolve();
      });
    });
  });

  // Handle upload complete webhook
  fastify.post('/uploads/complete', async (request, reply) => {
    const { fileId, metadata } = request.body as { fileId: string; metadata: Record<string, string> };

    try {
      // Extract metadata
      const tenantId = metadata.tenantId;
      const uploaderId = metadata.uploaderId;
      const filename = metadata.filename || 'video.webm';

      if (!tenantId || !uploaderId) {
        return reply.status(400).send({ error: 'Missing required metadata' });
      }

      // Get file info
      const filePath = path.join(UPLOAD_PATH, fileId);
      const stats = fs.statSync(filePath);
      const sizeMb = stats.size / (1024 * 1024);

      // Create video record in database
      const video = await prisma.video.create({
        data: {
          tenantId,
          uploaderId,
          title: filename.replace(/\.[^/.]+$/, ''), // Remove file extension
          storageUrl: `/uploads/${fileId}`,
          status: 'processing',
          sizeMb,
        },
      });

      // Queue video processing
      const serverUrl = `${request.protocol}://${request.hostname}`;
      const videoUrl = `${serverUrl}/uploads/${fileId}`;

      try {
        await queueVideoProcessing(video.id, videoUrl);
        console.log(`Queued video processing for ${video.id}`);
      } catch (error) {
        console.error('Error queuing video processing:', error);
        // Continue even if queuing fails, we'll handle it later
      }

      return reply.send(video);
    } catch (error) {
      console.error('Error handling upload complete:', error);
      return reply.status(500).send({ error: 'Failed to process upload' });
    }
  });

  // Serve uploaded files
  fastify.register(require('@fastify/static'), {
    root: UPLOAD_PATH,
    prefix: '/uploads/',
  });
}
