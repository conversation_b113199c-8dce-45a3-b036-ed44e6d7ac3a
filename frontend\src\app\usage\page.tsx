'use client';

import { useState, useEffect } from 'react';
import { Header } from '@/components/Header';
import Link from 'next/link';

interface UsageData {
  usage: {
    storage: { used: number; limit: number; percentage: number };
    videos: { used: number; limit: number; percentage: number };
    apiCalls: { used: number; limit: number; percentage: number };
  };
  limits: {
    storageGB: number;
    videosPerMonth: number;
    maxVideoDurationMin: number;
    maxVideoSizeMB: number;
    apiCallsPerDay: number;
  };
}

export default function UsagePage() {
  const [usageData, setUsageData] = useState<UsageData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchUsage() {
      try {
        const response = await fetch('/api/usage');
        
        if (!response.ok) {
          throw new Error('Failed to fetch usage statistics');
        }
        
        const data = await response.json();
        setUsageData(data);
      } catch (error) {
        console.error('Error fetching usage statistics:', error);
        setError('Failed to load usage statistics');
      } finally {
        setLoading(false);
      }
    }
    
    fetchUsage();
  }, []);

  const formatStorageSize = (gb: number) => {
    if (gb < 1) {
      return `${Math.round(gb * 1024)} MB`;
    }
    return `${gb.toFixed(1)} GB`;
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-semibold font-heading">Usage & Limits</h1>
          
          <Link href="/settings/plan" className="btn-primary">
            Upgrade Plan
          </Link>
        </div>
        
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="card py-12 text-center">
            <h2 className="text-xl font-medium mb-4 text-error">Error</h2>
            <p className="text-foreground/70 mb-6">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="btn-primary inline-block"
            >
              Try Again
            </button>
          </div>
        ) : usageData ? (
          <div className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="card">
                <h2 className="text-lg font-medium mb-4">Storage</h2>
                <div className="mb-2 flex justify-between items-center">
                  <span className="text-foreground/70">
                    {formatStorageSize(usageData.usage.storage.used)} of {formatStorageSize(usageData.usage.storage.limit)}
                  </span>
                  <span className="text-sm font-medium">
                    {Math.round(usageData.usage.storage.percentage)}%
                  </span>
                </div>
                <div className="w-full bg-foreground/10 rounded-full h-2.5">
                  <div
                    className={`h-2.5 rounded-full ${
                      usageData.usage.storage.percentage > 90
                        ? 'bg-error'
                        : usageData.usage.storage.percentage > 75
                        ? 'bg-warning'
                        : 'bg-success'
                    }`}
                    style={{ width: `${Math.min(100, usageData.usage.storage.percentage)}%` }}
                  ></div>
                </div>
              </div>
              
              <div className="card">
                <h2 className="text-lg font-medium mb-4">Videos This Month</h2>
                <div className="mb-2 flex justify-between items-center">
                  <span className="text-foreground/70">
                    {usageData.usage.videos.used} of {usageData.usage.videos.limit}
                  </span>
                  <span className="text-sm font-medium">
                    {Math.round(usageData.usage.videos.percentage)}%
                  </span>
                </div>
                <div className="w-full bg-foreground/10 rounded-full h-2.5">
                  <div
                    className={`h-2.5 rounded-full ${
                      usageData.usage.videos.percentage > 90
                        ? 'bg-error'
                        : usageData.usage.videos.percentage > 75
                        ? 'bg-warning'
                        : 'bg-success'
                    }`}
                    style={{ width: `${Math.min(100, usageData.usage.videos.percentage)}%` }}
                  ></div>
                </div>
              </div>
              
              <div className="card">
                <h2 className="text-lg font-medium mb-4">API Calls Today</h2>
                <div className="mb-2 flex justify-between items-center">
                  <span className="text-foreground/70">
                    {usageData.usage.apiCalls.used} of {usageData.usage.apiCalls.limit}
                  </span>
                  <span className="text-sm font-medium">
                    {Math.round(usageData.usage.apiCalls.percentage)}%
                  </span>
                </div>
                <div className="w-full bg-foreground/10 rounded-full h-2.5">
                  <div
                    className={`h-2.5 rounded-full ${
                      usageData.usage.apiCalls.percentage > 90
                        ? 'bg-error'
                        : usageData.usage.apiCalls.percentage > 75
                        ? 'bg-warning'
                        : 'bg-success'
                    }`}
                    style={{ width: `${Math.min(100, usageData.usage.apiCalls.percentage)}%` }}
                  ></div>
                </div>
              </div>
            </div>
            
            <div className="card">
              <h2 className="text-xl font-medium mb-6">Plan Limits</h2>
              
              <div className="overflow-hidden rounded-xl border border-foreground/10 bg-white dark:bg-foreground/5">
                <table className="w-full">
                  <thead className="bg-foreground/5">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-foreground/70 uppercase tracking-wider">Feature</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-foreground/70 uppercase tracking-wider">Limit</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-foreground/10">
                    <tr>
                      <td className="px-6 py-4">
                        <div className="font-medium">Storage</div>
                        <div className="text-sm text-foreground/70">Total storage for all videos</div>
                      </td>
                      <td className="px-6 py-4 text-right font-medium">
                        {formatStorageSize(usageData.limits.storageGB)}
                      </td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4">
                        <div className="font-medium">Videos Per Month</div>
                        <div className="text-sm text-foreground/70">Number of videos you can upload each month</div>
                      </td>
                      <td className="px-6 py-4 text-right font-medium">
                        {usageData.limits.videosPerMonth}
                      </td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4">
                        <div className="font-medium">Max Video Duration</div>
                        <div className="text-sm text-foreground/70">Maximum length of a single video</div>
                      </td>
                      <td className="px-6 py-4 text-right font-medium">
                        {usageData.limits.maxVideoDurationMin} minutes
                      </td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4">
                        <div className="font-medium">Max Video Size</div>
                        <div className="text-sm text-foreground/70">Maximum file size for a single video</div>
                      </td>
                      <td className="px-6 py-4 text-right font-medium">
                        {usageData.limits.maxVideoSizeMB / 1000} GB
                      </td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4">
                        <div className="font-medium">API Calls Per Day</div>
                        <div className="text-sm text-foreground/70">Number of API requests allowed per day</div>
                      </td>
                      <td className="px-6 py-4 text-right font-medium">
                        {usageData.limits.apiCallsPerDay}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            
            <div className="card bg-foreground/5 border border-foreground/10">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 bg-primary/10 p-3 rounded-full">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6 text-primary">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                    <circle cx="9" cy="7" r="4" />
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                    <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-2">Need More Resources?</h3>
                  <p className="text-foreground/70 mb-4">
                    Upgrade your plan to get more storage, higher limits, and additional features.
                  </p>
                  <Link href="/settings/plan" className="btn-primary">
                    View Plans
                  </Link>
                </div>
              </div>
            </div>
          </div>
        ) : null}
      </main>
    </div>
  );
}
