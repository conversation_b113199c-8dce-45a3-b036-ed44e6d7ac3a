'use client';

import { useState, useEffect } from 'react';
import { Header } from '@/components/Header';
import Link from 'next/link';
import { formatDate } from '@/lib/utils';

interface ActionRule {
  id: string;
  name: string;
  trigger: {
    event: string;
    conditions?: Record<string, any>[];
  };
  destination: string;
  createdAt: string;
}

export default function ActionsPage() {
  const [actionRules, setActionRules] = useState<ActionRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchActionRules() {
      try {
        const response = await fetch('/api/actions');
        
        if (!response.ok) {
          throw new Error('Failed to fetch action rules');
        }
        
        const data = await response.json();
        setActionRules(data);
      } catch (error) {
        console.error('Error fetching action rules:', error);
        setError('Failed to load action rules');
      } finally {
        setLoading(false);
      }
    }
    
    fetchActionRules();
  }, []);

  const getEventIcon = (event: string) => {
    switch (event) {
      case 'video.created':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-primary">
            <rect x="2" y="2" width="20" height="20" rx="2.18" ry="2.18" />
            <line x1="7" y1="2" x2="7" y2="22" />
            <line x1="17" y1="2" x2="17" y2="22" />
            <line x1="2" y1="12" x2="22" y2="12" />
            <line x1="2" y1="7" x2="7" y2="7" />
            <line x1="2" y1="17" x2="7" y2="17" />
            <line x1="17" y1="17" x2="22" y2="17" />
            <line x1="17" y1="7" x2="22" y2="7" />
          </svg>
        );
      case 'video.ready':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-success">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
            <polyline points="22 4 12 14.01 9 11.01" />
          </svg>
        );
      case 'video.error':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-error">
            <circle cx="12" cy="12" r="10" />
            <line x1="12" y1="8" x2="12" y2="12" />
            <line x1="12" y1="16" x2="12.01" y2="16" />
          </svg>
        );
      case 'video.viewed':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-primary">
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
            <circle cx="12" cy="12" r="3" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-foreground/70">
            <circle cx="12" cy="12" r="10" />
            <line x1="12" y1="16" x2="12" y2="12" />
            <line x1="12" y1="8" x2="12.01" y2="8" />
          </svg>
        );
    }
  };

  const getDestinationIcon = (destination: string) => {
    if (destination.includes('hooks.slack.com')) {
      return (
        <svg viewBox="0 0 24 24" fill="currentColor" className="h-5 w-5 text-[#4A154B]">
          <path d="M5.042 15.165a2.528 2.528 0 0 1-2.52 2.523A2.528 2.528 0 0 1 0 15.165a2.527 2.527 0 0 1 2.522-2.52h2.52v2.52zM6.313 15.165a2.527 2.527 0 0 1 2.521-2.52 2.527 2.527 0 0 1 2.521 2.52v6.313A2.528 2.528 0 0 1 8.834 24a2.528 2.528 0 0 1-2.521-2.522v-6.313zM8.834 5.042a2.528 2.528 0 0 1-2.521-2.52A2.528 2.528 0 0 1 8.834 0a2.528 2.528 0 0 1 2.521 2.522v2.52H8.834zM8.834 6.313a2.528 2.528 0 0 1 2.521 2.521 2.528 2.528 0 0 1-2.521 2.521H2.522A2.528 2.528 0 0 1 0 8.834a2.528 2.528 0 0 1 2.522-2.521h6.312zM18.956 8.834a2.528 2.528 0 0 1 2.522-2.521A2.528 2.528 0 0 1 24 8.834a2.528 2.528 0 0 1-2.522 2.521h-2.522V8.834zM17.688 8.834a2.528 2.528 0 0 1-2.523 2.521 2.527 2.527 0 0 1-2.52-2.521V2.522A2.527 2.527 0 0 1 15.165 0a2.528 2.528 0 0 1 2.523 2.522v6.312zM15.165 18.956a2.528 2.528 0 0 1 2.523 2.522A2.528 2.528 0 0 1 15.165 24a2.527 2.527 0 0 1-2.52-2.522v-2.522h2.52zM15.165 17.688a2.527 2.527 0 0 1-2.52-2.523 2.526 2.526 0 0 1 2.52-2.52h6.313A2.527 2.527 0 0 1 24 15.165a2.528 2.528 0 0 1-2.522 2.523h-6.313z" />
        </svg>
      );
    } else if (destination.includes('zendesk.com')) {
      return (
        <svg viewBox="0 0 24 24" fill="currentColor" className="h-5 w-5 text-[#03363D]">
          <path d="M12 0C5.372 0 0 5.372 0 12s5.372 12 12 12 12-5.372 12-12S18.628 0 12 0zM1.655 12.976c0-1.86 1.51-3.37 3.37-3.37 1.86 0 3.37 1.51 3.37 3.37 0 1.86-1.51 3.37-3.37 3.37-1.86 0-3.37-1.51-3.37-3.37zm8.478-3.37c0-1.86 1.51-3.37 3.37-3.37 1.86 0 3.37 1.51 3.37 3.37 0 1.86-1.51 3.37-3.37 3.37-1.86 0-3.37-1.51-3.37-3.37zm8.478 3.37c0-1.86 1.51-3.37 3.37-3.37 1.86 0 3.37 1.51 3.37 3.37 0 1.86-1.51 3.37-3.37 3.37-1.86 0-3.37-1.51-3.37-3.37zm-4.239 4.239c0 1.86-1.51 3.37-3.37 3.37-1.86 0-3.37-1.51-3.37-3.37 0-1.86 1.51-3.37 3.37-3.37 1.86 0 3.37 1.51 3.37 3.37z" />
        </svg>
      );
    } else if (destination.includes('api.hubspot.com')) {
      return (
        <svg viewBox="0 0 24 24" fill="currentColor" className="h-5 w-5 text-[#FF7A59]">
          <path d="M22.447 9.602h-4.978V4.623a1.55 1.55 0 0 0-1.553-1.552 1.55 1.55 0 0 0-1.553 1.552v4.979h-4.979a1.55 1.55 0 0 0-1.552 1.553 1.55 1.55 0 0 0 1.552 1.553h4.979v4.978a1.55 1.55 0 0 0 1.553 1.553 1.55 1.55 0 0 0 1.553-1.553v-4.978h4.978a1.55 1.55 0 0 0 1.553-1.553 1.55 1.55 0 0 0-1.553-1.553zm-9.55 6.42v-.304c-.005-1.042-.847-1.884-1.889-1.889h-.304c-1.042.005-1.884.847-1.889 1.889v.304c.005 1.042.847 1.884 1.889 1.889h.304c1.042-.005 1.884-.847 1.889-1.889zm-6.42-6.42v-.304c-.005-1.042-.847-1.884-1.889-1.889H4.284c-1.042.005-1.884.847-1.889 1.889v.304c.005 1.042.847 1.884 1.889 1.889h.304c1.042-.005 1.884-.847 1.889-1.889zm6.42-6.419v-.304c-.005-1.042-.847-1.884-1.889-1.889h-.304c-1.042.005-1.884.847-1.889 1.889v.304c.005 1.042.847 1.884 1.889 1.889h.304c1.042-.005 1.884-.847 1.889-1.889z" />
        </svg>
      );
    } else {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-primary">
          <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
          <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
        </svg>
      );
    }
  };

  const formatEvent = (event: string) => {
    switch (event) {
      case 'video.created':
        return 'Video Created';
      case 'video.ready':
        return 'Video Ready';
      case 'video.error':
        return 'Video Error';
      case 'video.viewed':
        return 'Video Viewed';
      default:
        return event;
    }
  };

  const formatDestination = (destination: string) => {
    if (destination.includes('hooks.slack.com')) {
      return 'Slack Webhook';
    } else if (destination.includes('zendesk.com')) {
      return 'Zendesk API';
    } else if (destination.includes('api.hubspot.com')) {
      return 'HubSpot API';
    } else {
      // Try to extract domain
      try {
        const url = new URL(destination);
        return url.hostname;
      } catch (e) {
        return destination;
      }
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-semibold font-heading">Action Rules</h1>
          
          <Link href="/actions/new" className="btn-primary">
            Create Rule
          </Link>
        </div>
        
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="card py-12 text-center">
            <h2 className="text-xl font-medium mb-4 text-error">Error</h2>
            <p className="text-foreground/70 mb-6">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="btn-primary inline-block"
            >
              Try Again
            </button>
          </div>
        ) : actionRules.length === 0 ? (
          <div className="card py-12 text-center">
            <h2 className="text-xl font-medium mb-4">No Action Rules Yet</h2>
            <p className="text-foreground/70 mb-6">
              You haven't created any action rules yet. Action rules let you trigger webhooks and integrations when certain events happen.
            </p>
            <Link href="/actions/new" className="btn-primary inline-block">
              Create Your First Rule
            </Link>
          </div>
        ) : (
          <div className="overflow-hidden rounded-xl border border-foreground/10 bg-white dark:bg-foreground/5">
            <table className="w-full">
              <thead className="bg-foreground/5">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-foreground/70 uppercase tracking-wider">Name</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-foreground/70 uppercase tracking-wider">Trigger</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-foreground/70 uppercase tracking-wider">Destination</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-foreground/70 uppercase tracking-wider">Created</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-foreground/70 uppercase tracking-wider"></th>
                </tr>
              </thead>
              <tbody className="divide-y divide-foreground/10">
                {actionRules.map((rule) => (
                  <tr key={rule.id} className="hover:bg-foreground/5">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="font-medium">{rule.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getEventIcon(rule.trigger.event)}
                        <span className="ml-2">{formatEvent(rule.trigger.event)}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getDestinationIcon(rule.destination)}
                        <span className="ml-2">{formatDestination(rule.destination)}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground/70">
                      {formatDate(rule.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Link href={`/actions/${rule.id}`} className="text-primary hover:text-primary-hover">
                        Edit
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
        
        <div className="mt-12">
          <h2 className="text-2xl font-semibold font-heading mb-6">Available Events</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="card">
              <div className="flex items-center gap-3 mb-3">
                {getEventIcon('video.created')}
                <h3 className="text-lg font-medium">Video Created</h3>
              </div>
              <p className="text-sm text-foreground/70">
                Triggered when a new video is uploaded to your account.
              </p>
            </div>
            
            <div className="card">
              <div className="flex items-center gap-3 mb-3">
                {getEventIcon('video.ready')}
                <h3 className="text-lg font-medium">Video Ready</h3>
              </div>
              <p className="text-sm text-foreground/70">
                Triggered when a video is processed and ready to view with transcription.
              </p>
            </div>
            
            <div className="card">
              <div className="flex items-center gap-3 mb-3">
                {getEventIcon('video.error')}
                <h3 className="text-lg font-medium">Video Error</h3>
              </div>
              <p className="text-sm text-foreground/70">
                Triggered when there's an error processing a video.
              </p>
            </div>
            
            <div className="card">
              <div className="flex items-center gap-3 mb-3">
                {getEventIcon('video.viewed')}
                <h3 className="text-lg font-medium">Video Viewed</h3>
              </div>
              <p className="text-sm text-foreground/70">
                Triggered when someone views your video.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
