import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { PrismaClient } from '@prisma/client';
import { verifyAuth } from '../middleware/auth';

const prisma = new PrismaClient();

// Type definitions
interface CreateVideoRequest {
  title?: string;
  storageUrl: string;
  thumbnailUrl?: string;
  durationSec?: number;
  sizeMb?: number;
}

interface UpdateVideoRequest {
  title?: string;
  status?: string;
  thumbnailUrl?: string;
  durationSec?: number;
  sizeMb?: number;
}

interface VideoParams {
  id: string;
}

interface AuthenticatedRequest extends FastifyRequest {
  user?: {
    id: string;
    clerkId: string;
    email: string;
    name: string;
    tenantId: string;
  };
}

export default async function (fastify: FastifyInstance) {
  // Authentication middleware
  fastify.addHook('onRequest', verifyAuth);

  // Get all videos for the authenticated user's tenant
  fastify.get('/', async (request: AuthenticatedRequest, reply: FastifyReply) => {
    if (!request.user) {
      return reply.status(401).send({ error: 'Unauthorized' });
    }

    const { tenantId } = request.user;

    try {
      const videos = await prisma.video.findMany({
        where: { tenantId },
        include: {
          uploader: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          views: true,
          transcript: {
            select: {
              id: true,
              language: true,
              confidence: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      // Transform the videos to include view count
      const transformedVideos = videos.map(video => ({
        ...video,
        viewCount: video.views.length,
      }));

      return reply.send(transformedVideos);
    } catch (error) {
      console.error('Error fetching videos:', error);
      return reply.status(500).send({ error: 'Failed to fetch videos' });
    }
  });

  // Get a single video by ID
  fastify.get('/:id', async (request: FastifyRequest<{ Params: VideoParams }>, reply: FastifyReply) => {
    const { id } = request.params;

    try {
      const video = await prisma.video.findUnique({
        where: { id },
        include: {
          uploader: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          views: {
            select: {
              id: true,
              viewerEmail: true,
              startedAt: true,
              endedAt: true,
            },
          },
          transcript: {
            include: {
              chunks: true,
            },
          },
        },
      });

      if (!video) {
        return reply.status(404).send({ error: 'Video not found' });
      }

      return reply.send(video);
    } catch (error) {
      console.error('Error fetching video:', error);
      return reply.status(500).send({ error: 'Failed to fetch video' });
    }
  });

  // Create a new video
  fastify.post('/', async (request: AuthenticatedRequest, reply: FastifyReply) => {
    if (!request.user) {
      return reply.status(401).send({ error: 'Unauthorized' });
    }

    const { title, storageUrl, thumbnailUrl, durationSec, sizeMb } = request.body as CreateVideoRequest;
    const { id: uploaderId, tenantId } = request.user;

    if (!storageUrl) {
      return reply.status(400).send({ error: 'Storage URL is required' });
    }

    try {
      const video = await prisma.video.create({
        data: {
          title: title || 'Untitled Video',
          tenantId,
          uploaderId,
          storageUrl,
          thumbnailUrl,
          durationSec,
          sizeMb,
          status: 'processing',
        },
      });

      // In a real implementation, we would trigger a worker to process the video
      // For example:
      // await queueVideoProcessing(video.id);

      return reply.status(201).send(video);
    } catch (error) {
      console.error('Error creating video:', error);
      return reply.status(500).send({ error: 'Failed to create video' });
    }
  });

  // Update a video
  fastify.patch('/:id', async (request: FastifyRequest<{ Params: VideoParams; Body: UpdateVideoRequest }>, reply: FastifyReply) => {
    const { id } = request.params;
    const { status, thumbnailUrl, durationSec, sizeMb } = request.body;

    try {
      const video = await prisma.video.update({
        where: { id },
        data: {
          status,
          thumbnailUrl,
          durationSec,
          sizeMb,
        },
      });

      return reply.send(video);
    } catch (error) {
      console.error('Error updating video:', error);
      return reply.status(500).send({ error: 'Failed to update video' });
    }
  });

  // Delete a video
  fastify.delete('/:id', async (request: FastifyRequest<{ Params: VideoParams }>, reply: FastifyReply) => {
    const { id } = request.params;

    try {
      await prisma.video.delete({
        where: { id },
      });

      return reply.status(204).send();
    } catch (error) {
      console.error('Error deleting video:', error);
      return reply.status(500).send({ error: 'Failed to delete video' });
    }
  });

  // Record a view for a video
  fastify.post('/:id/views', async (request: FastifyRequest<{ Params: VideoParams; Body: { viewerEmail?: string; ip?: string } }>, reply: FastifyReply) => {
    const { id } = request.params;
    const { viewerEmail, ip } = request.body;

    try {
      const view = await prisma.view.create({
        data: {
          videoId: id,
          viewerEmail,
          ip,
        },
      });

      return reply.status(201).send(view);
    } catch (error) {
      console.error('Error recording view:', error);
      return reply.status(500).send({ error: 'Failed to record view' });
    }
  });
}
