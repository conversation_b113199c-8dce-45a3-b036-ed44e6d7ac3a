{"version": 3, "file": "sandbox.js", "sourceRoot": "", "sources": ["../../../src/classes/sandbox.ts"], "names": [], "mappings": ";;AAAA,oCAAuD;AAMvD,MAAM,OAAO,GAAG,CACd,WAAgB,EAChB,SAAoB,EACpB,EAAE;IACF,OAAO,KAAK,UAAU,OAAO,CAAC,GAAiB,EAAE,KAAc;QAC7D,IAAI,KAAY,CAAC;QACjB,IAAI,UAAe,CAAC;QACpB,IAAI,WAAgB,CAAC;QACrB,IAAI;YACF,MAAM,IAAI,GAAe,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACvD,MAAM,SAAS,GAAG,KAAK,IAAI,EAAE;oBAC3B,IAAI;wBACF,WAAW,GAAG,CAAC,QAAa,EAAE,MAAW,EAAE,EAAE;4BAC3C,MAAM,CACJ,IAAI,KAAK,CACP,wBAAwB,GAAG,QAAQ,GAAG,WAAW,GAAG,MAAM,CAC3D,CACF,CAAC;wBACJ,CAAC,CAAC;wBAEF,KAAK,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;wBAC5C,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;wBAE9B,UAAU,GAAG,KAAK,EAAE,GAAiB,EAAE,EAAE;;4BACvC,IAAI;gCACF,QAAQ,GAAG,CAAC,GAAG,EAAE;oCACf,KAAK,qBAAa,CAAC,SAAS;wCAC1B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wCACnB,MAAM;oCACR,KAAK,qBAAa,CAAC,MAAM,CAAC;oCAC1B,KAAK,qBAAa,CAAC,KAAK,CAAC,CAAC;wCACxB,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC;wCACxB,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;wCAC9B,MAAM,CAAC,GAAG,CAAC,CAAC;wCACZ,MAAM;qCACP;oCACD,KAAK,qBAAa,CAAC,QAAQ;wCACzB,MAAM,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wCACpC,MAAM;oCACR,KAAK,qBAAa,CAAC,GAAG;wCACpB,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wCACzB,MAAM;oCACR,KAAK,qBAAa,CAAC,aAAa;wCAC9B,MAAM,GAAG,CAAC,aAAa,CACrB,MAAA,GAAG,CAAC,KAAK,0CAAE,SAAS,EACpB,MAAA,GAAG,CAAC,KAAK,0CAAE,KAAK,CACjB,CAAC;wCACF,MAAM;oCACR,KAAK,qBAAa,CAAC,MAAM;wCACvB,MAAM,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wCAChC,MAAM;oCACR,KAAK,qBAAa,CAAC,iBAAiB;wCAClC;4CACE,MAAM,KAAK,GAAG,MAAM,GAAG,CAAC,iBAAiB,EAAE,CAAC;4CAC5C,KAAK,CAAC,IAAI,CAAC;gDACT,SAAS,EAAE,GAAG,CAAC,SAAS;gDACxB,GAAG,EAAE,oBAAY,CAAC,yBAAyB;gDAC3C,KAAK;6CACN,CAAC,CAAC;yCACJ;wCACD,MAAM;iCACT;6BACF;4BAAC,OAAO,GAAG,EAAE;gCACZ,MAAM,CAAC,GAAG,CAAC,CAAC;6BACb;wBACH,CAAC,CAAC;wBAEF,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;wBAEhC,KAAK,CAAC,IAAI,CAAC;4BACT,GAAG,EAAE,oBAAY,CAAC,KAAK;4BACvB,GAAG,EAAE,GAAG,CAAC,aAAa,EAAE;4BACxB,KAAK;yBACN,CAAC,CAAC;qBACJ;oBAAC,OAAO,KAAK,EAAE;wBACd,MAAM,CAAC,KAAK,CAAC,CAAC;qBACf;gBACH,CAAC,CAAC;gBACF,SAAS,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC;YACX,OAAO,IAAI,CAAC;SACb;gBAAS;YACR,IAAI,KAAK,EAAE;gBACT,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBACjC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gBAC/B,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,IAAI,KAAK,CAAC,UAAU,KAAK,IAAI,EAAE;oBACxD,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;iBAC1B;aACF;SACF;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,kBAAe,OAAO,CAAC"}