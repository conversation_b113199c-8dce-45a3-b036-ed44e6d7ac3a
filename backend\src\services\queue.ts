import { Queue } from 'bullmq';

// Define Redis connection
const connection = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
};

// Create queues
const videoQueue = new Queue('video', { connection });
const transcriptionQueue = new Queue('transcription', { connection });

/**
 * Queue a video for processing
 */
export async function queueVideoProcessing(videoId: string, videoUrl: string): Promise<void> {
  await videoQueue.add(
    `video-${videoId}`,
    {
      videoId,
      videoUrl,
    },
    { removeOnComplete: true }
  );
}

/**
 * Queue a transcription job
 */
export async function queueTranscription(videoId: string, audioUrl: string): Promise<void> {
  await transcriptionQueue.add(
    `transcription-${videoId}`,
    {
      videoId,
      audioUrl,
    },
    { removeOnComplete: true }
  );
}

export default {
  queueVideoProcessing,
  queueTranscription,
};
