import { PrismaClient } from '@prisma/client';
import slack from '../integrations/slack';
import zendesk from '../integrations/zendesk';
import hubspot from '../integrations/hubspot';
import { processVideoRules } from './webhook';

const prisma = new PrismaClient();

/**
 * Trigger integrations for a video event
 */
export async function triggerVideoEvent(videoId: string, event: string): Promise<void> {
  try {
    // Process webhook rules
    await processVideoRules(videoId, event);
    
    // Get the video with all related data
    const video = await prisma.video.findUnique({
      where: { id: videoId },
      include: {
        tenant: true,
        uploader: true,
        transcript: {
          include: {
            chunks: true,
          },
        },
      },
    });
    
    if (!video) {
      throw new Error(`Video ${videoId} not found`);
    }
    
    // Get tenant integrations
    const integrations = await prisma.integration.findMany({
      where: {
        tenantId: video.tenantId,
        enabled: true,
      },
    });
    
    // Process each integration
    for (const integration of integrations) {
      try {
        await processIntegration(integration, video, event);
      } catch (error) {
        console.error(`Error processing integration ${integration.id}:`, error);
      }
    }
  } catch (error) {
    console.error(`Error triggering video event ${event} for ${videoId}:`, error);
  }
}

/**
 * Process an integration for a video event
 */
async function processIntegration(integration: any, video: any, event: string): Promise<void> {
  const { type, config } = integration;
  
  switch (type) {
    case 'slack':
      await processSlackIntegration(config, video, event);
      break;
    case 'zendesk':
      await processZendeskIntegration(config, video, event);
      break;
    case 'hubspot':
      await processHubSpotIntegration(config, video, event);
      break;
    default:
      console.warn(`Unknown integration type: ${type}`);
  }
}

/**
 * Process a Slack integration
 */
async function processSlackIntegration(config: any, video: any, event: string): Promise<void> {
  if (!config.webhookUrl) {
    console.error('Slack webhook URL is missing');
    return;
  }
  
  // Only send messages for certain events
  if (event === 'video.ready' || event === 'video.created') {
    const message = slack.createVideoMessage(video);
    await slack.sendSlackWebhook(config.webhookUrl, message);
  }
}

/**
 * Process a Zendesk integration
 */
async function processZendeskIntegration(config: any, video: any, event: string): Promise<void> {
  if (!config.subdomain || !config.email || !config.apiToken) {
    console.error('Zendesk configuration is incomplete');
    return;
  }
  
  // Only create tickets for certain events
  if (event === 'video.ready') {
    const ticket = zendesk.createVideoTicket(video);
    await zendesk.createZendeskTicket(config.subdomain, config.email, config.apiToken, ticket);
  }
}

/**
 * Process a HubSpot integration
 */
async function processHubSpotIntegration(config: any, video: any, event: string): Promise<void> {
  if (!config.apiKey || !config.contactId) {
    console.error('HubSpot configuration is incomplete');
    return;
  }
  
  // Only create notes/tasks for certain events
  if (event === 'video.ready') {
    // Create a note
    const note = hubspot.createVideoNote(video, config.contactId);
    await hubspot.createHubSpotNote(config.apiKey, note);
    
    // Create a task
    const task = hubspot.createVideoTask(video, config.contactId);
    await hubspot.createHubSpotTask(config.apiKey, task);
  }
}

export default {
  triggerVideoEvent,
};
