import Fastify from 'fastify';
import cors from '@fastify/cors';
import jwt from '@fastify/jwt';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create Fastify instance
const server = Fastify({
  logger: true,
});

// Register plugins
server.register(cors, {
  origin: true, // Allow all origins in development
});

server.register(jwt, {
  secret: process.env.JWT_SECRET || 'default-secret-change-in-production',
});

// Health check route
server.get('/health', async () => {
  return { status: 'ok' };
});

// Import middleware
import { trackUsage } from './middleware/usage';

// Import routes
import videoRoutes from './routes/videos';
import livekitRoutes from './routes/livekit';
import uploadsRoutes from './routes/uploads';
import actionsRoutes from './routes/actions';
import integrationsRoutes from './routes/integrations';
import usageRoutes from './routes/usage';

// Add global middleware
server.addHook('onRequest', trackUsage);

// Register routes
server.register(videoRoutes, { prefix: '/api/videos' });
server.register(livekitRoutes, { prefix: '/api/livekit' });
server.register(uploadsRoutes);
server.register(actionsRoutes, { prefix: '/api/actions' });
server.register(integrationsRoutes, { prefix: '/api/integrations' });
server.register(usageRoutes, { prefix: '/api/usage' });

// Start server
const start = async () => {
  try {
    const port = process.env.PORT ? parseInt(process.env.PORT, 10) : 3001;
    await server.listen({ port, host: '0.0.0.0' });
    console.log(`Server is running on port ${port}`);
  } catch (err) {
    server.log.error(err);
    process.exit(1);
  }
};

start();
