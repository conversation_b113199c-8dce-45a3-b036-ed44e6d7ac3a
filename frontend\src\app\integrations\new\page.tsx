'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Header } from '@/components/Header';
import Link from 'next/link';

interface FormState {
  name: string;
  type: string;
  config: {
    // Slack
    webhookUrl?: string;
    
    // Zendesk
    subdomain?: string;
    email?: string;
    apiToken?: string;
    
    // HubSpot
    apiKey?: string;
    contactId?: string;
  };
  enabled: boolean;
}

export default function NewIntegrationPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const initialType = searchParams.get('type') || 'slack';
  
  const [formState, setFormState] = useState<FormState>({
    name: '',
    type: initialType,
    config: {},
    enabled: true,
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  
  useEffect(() => {
    // Set default name based on type
    let name = '';
    switch (formState.type) {
      case 'slack':
        name = 'Slack Integration';
        break;
      case 'zendesk':
        name = 'Zendesk Integration';
        break;
      case 'hubspot':
        name = 'HubSpot Integration';
        break;
    }
    
    setFormState(prev => ({
      ...prev,
      name,
    }));
  }, [formState.type]);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name.includes('.')) {
      // Handle nested properties (config.*)
      const [parent, child] = name.split('.');
      setFormState(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof FormState],
          [child]: value,
        },
      }));
    } else {
      setFormState(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };
  
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormState(prev => ({
      ...prev,
      [name]: checked,
    }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/integrations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formState),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create integration');
      }
      
      const data = await response.json();
      router.push('/integrations');
    } catch (error) {
      console.error('Error creating integration:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };
  
  const handleTest = async () => {
    setLoading(true);
    setTestResult(null);
    setError(null);
    
    try {
      // Create a temporary integration for testing
      const response = await fetch('/api/integrations/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: formState.type,
          config: formState.config,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to test integration');
      }
      
      const result = await response.json();
      setTestResult(result);
    } catch (error) {
      console.error('Error testing integration:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };
  
  const renderFormFields = () => {
    switch (formState.type) {
      case 'slack':
        return (
          <div className="space-y-4">
            <div>
              <label htmlFor="config.webhookUrl" className="block text-sm font-medium mb-1">
                Webhook URL <span className="text-error">*</span>
              </label>
              <input
                type="text"
                id="config.webhookUrl"
                name="config.webhookUrl"
                value={formState.config.webhookUrl || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-foreground/10 rounded-lg bg-foreground/5"
                placeholder="https://hooks.slack.com/services/..."
                required
              />
              <p className="text-xs text-foreground/70 mt-1">
                Create a webhook URL in your Slack workspace settings.
              </p>
            </div>
          </div>
        );
      
      case 'zendesk':
        return (
          <div className="space-y-4">
            <div>
              <label htmlFor="config.subdomain" className="block text-sm font-medium mb-1">
                Subdomain <span className="text-error">*</span>
              </label>
              <div className="flex items-center">
                <input
                  type="text"
                  id="config.subdomain"
                  name="config.subdomain"
                  value={formState.config.subdomain || ''}
                  onChange={handleInputChange}
                  className="flex-1 px-3 py-2 border border-foreground/10 rounded-lg bg-foreground/5"
                  placeholder="yourcompany"
                  required
                />
                <span className="ml-2 text-foreground/70">.zendesk.com</span>
              </div>
            </div>
            
            <div>
              <label htmlFor="config.email" className="block text-sm font-medium mb-1">
                Email <span className="text-error">*</span>
              </label>
              <input
                type="email"
                id="config.email"
                name="config.email"
                value={formState.config.email || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-foreground/10 rounded-lg bg-foreground/5"
                placeholder="<EMAIL>"
                required
              />
            </div>
            
            <div>
              <label htmlFor="config.apiToken" className="block text-sm font-medium mb-1">
                API Token <span className="text-error">*</span>
              </label>
              <input
                type="password"
                id="config.apiToken"
                name="config.apiToken"
                value={formState.config.apiToken || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-foreground/10 rounded-lg bg-foreground/5"
                placeholder="••••••••••••••••"
                required
              />
              <p className="text-xs text-foreground/70 mt-1">
                Generate an API token in your Zendesk admin settings.
              </p>
            </div>
          </div>
        );
      
      case 'hubspot':
        return (
          <div className="space-y-4">
            <div>
              <label htmlFor="config.apiKey" className="block text-sm font-medium mb-1">
                API Key <span className="text-error">*</span>
              </label>
              <input
                type="password"
                id="config.apiKey"
                name="config.apiKey"
                value={formState.config.apiKey || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-foreground/10 rounded-lg bg-foreground/5"
                placeholder="••••••••••••••••"
                required
              />
              <p className="text-xs text-foreground/70 mt-1">
                Generate an API key in your HubSpot developer settings.
              </p>
            </div>
            
            <div>
              <label htmlFor="config.contactId" className="block text-sm font-medium mb-1">
                Default Contact ID (Optional)
              </label>
              <input
                type="text"
                id="config.contactId"
                name="config.contactId"
                value={formState.config.contactId || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-foreground/10 rounded-lg bg-foreground/5"
                placeholder="123456789"
              />
              <p className="text-xs text-foreground/70 mt-1">
                If provided, notes and tasks will be associated with this contact by default.
              </p>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };
  
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="flex items-center gap-2 mb-8">
            <Link href="/integrations" className="text-foreground/70 hover:text-foreground">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                <path d="M19 12H5M12 19l-7-7 7-7" />
              </svg>
            </Link>
            <h1 className="text-3xl font-semibold font-heading">Add Integration</h1>
          </div>
          
          <div className="card">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="type" className="block text-sm font-medium mb-1">
                  Integration Type <span className="text-error">*</span>
                </label>
                <select
                  id="type"
                  name="type"
                  value={formState.type}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-foreground/10 rounded-lg bg-foreground/5"
                  required
                >
                  <option value="slack">Slack</option>
                  <option value="zendesk">Zendesk</option>
                  <option value="hubspot">HubSpot</option>
                </select>
              </div>
              
              <div>
                <label htmlFor="name" className="block text-sm font-medium mb-1">
                  Integration Name <span className="text-error">*</span>
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formState.name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-foreground/10 rounded-lg bg-foreground/5"
                  placeholder="My Integration"
                  required
                />
              </div>
              
              <div className="border-t border-foreground/10 pt-6">
                <h2 className="text-lg font-medium mb-4">Configuration</h2>
                {renderFormFields()}
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enabled"
                  name="enabled"
                  checked={formState.enabled}
                  onChange={handleCheckboxChange}
                  className="h-4 w-4 text-primary border-foreground/30 rounded"
                />
                <label htmlFor="enabled" className="ml-2 block text-sm">
                  Enable this integration
                </label>
              </div>
              
              {error && (
                <div className="bg-error/10 text-error px-4 py-3 rounded-lg text-sm">
                  {error}
                </div>
              )}
              
              {testResult && (
                <div className={`${
                  testResult.success ? 'bg-success/10 text-success' : 'bg-error/10 text-error'
                } px-4 py-3 rounded-lg text-sm`}>
                  {testResult.message}
                </div>
              )}
              
              <div className="flex gap-4 pt-2">
                <button
                  type="submit"
                  className="btn-primary flex-1"
                  disabled={loading}
                >
                  {loading ? 'Saving...' : 'Save Integration'}
                </button>
                
                <button
                  type="button"
                  onClick={handleTest}
                  className="btn-secondary"
                  disabled={loading}
                >
                  Test Connection
                </button>
              </div>
            </form>
          </div>
        </div>
      </main>
    </div>
  );
}
