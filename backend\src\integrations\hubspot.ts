import fetch from 'node-fetch';

interface HubSpotNote {
  properties: {
    hs_note_body: string;
    hs_timestamp?: string;
  };
  associations?: Array<{
    to: {
      id: string;
    };
    types: Array<{
      associationCategory: string;
      associationTypeId: number;
    }>;
  }>;
}

interface HubSpotTask {
  properties: {
    hs_task_body: string;
    hs_task_subject: string;
    hs_task_status?: 'NOT_STARTED' | 'IN_PROGRESS' | 'WAITING' | 'COMPLETED';
    hs_task_priority?: 'HIGH' | 'MEDIUM' | 'LOW';
    hs_timestamp?: string;
    hs_task_type?: string;
  };
  associations?: Array<{
    to: {
      id: string;
    };
    types: Array<{
      associationCategory: string;
      associationTypeId: number;
    }>;
  }>;
}

/**
 * Create a note in HubSpot
 */
export async function createHubSpotNote(
  apiKey: string,
  note: HubSpotNote
): Promise<{ success: boolean; noteId?: string; error?: string }> {
  try {
    const response = await fetch('https://api.hubapi.com/crm/v3/objects/notes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify(note),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      return {
        success: false,
        error: `HubSpot API error: ${response.status} - ${errorText}`,
      };
    }
    
    const data = await response.json();
    
    return {
      success: true,
      noteId: data.id,
    };
  } catch (error) {
    console.error('Error creating HubSpot note:', error);
    
    return {
      success: false,
      error: `Error creating HubSpot note: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Create a task in HubSpot
 */
export async function createHubSpotTask(
  apiKey: string,
  task: HubSpotTask
): Promise<{ success: boolean; taskId?: string; error?: string }> {
  try {
    const response = await fetch('https://api.hubapi.com/crm/v3/objects/tasks', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify(task),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      return {
        success: false,
        error: `HubSpot API error: ${response.status} - ${errorText}`,
      };
    }
    
    const data = await response.json();
    
    return {
      success: true,
      taskId: data.id,
    };
  } catch (error) {
    console.error('Error creating HubSpot task:', error);
    
    return {
      success: false,
      error: `Error creating HubSpot task: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Create a HubSpot note for a video
 */
export function createVideoNote(video: any, contactId: string): HubSpotNote {
  const videoUrl = `${process.env.FRONTEND_URL || 'https://echopilot.example.com'}/v/${video.id}`;
  
  let body = `<h3>New Video: ${video.title || 'Untitled Video'}</h3>`;
  
  if (video.durationSec) {
    body += `<p>Duration: ${formatDuration(video.durationSec)}</p>`;
  }
  
  if (video.summary) {
    body += `<p><strong>Summary:</strong><br>${video.summary}</p>`;
  }
  
  if (video.sentiment) {
    body += `<p><strong>Sentiment:</strong> ${video.sentiment} (${Math.round((video.sentimentConfidence || 0) * 100)}% confidence)</p>`;
  }
  
  body += `<p><a href="${videoUrl}">View the video</a></p>`;
  
  return {
    properties: {
      hs_note_body: body,
      hs_timestamp: new Date().getTime().toString(),
    },
    associations: [
      {
        to: {
          id: contactId,
        },
        types: [
          {
            associationCategory: 'HUBSPOT_DEFINED',
            associationTypeId: 202, // Contact to note association
          },
        ],
      },
    ],
  };
}

/**
 * Create a HubSpot task for a video
 */
export function createVideoTask(video: any, contactId: string): HubSpotTask {
  const videoUrl = `${process.env.FRONTEND_URL || 'https://echopilot.example.com'}/v/${video.id}`;
  
  let body = `Review the video: ${video.title || 'Untitled Video'}\n`;
  
  if (video.summary) {
    body += `\nSummary: ${video.summary}\n`;
  }
  
  body += `\nVideo link: ${videoUrl}`;
  
  return {
    properties: {
      hs_task_subject: `Review video: ${video.title || 'Untitled Video'}`,
      hs_task_body: body,
      hs_task_status: 'NOT_STARTED',
      hs_task_priority: 'MEDIUM',
      hs_timestamp: new Date().getTime().toString(),
      hs_task_type: 'REVIEW',
    },
    associations: [
      {
        to: {
          id: contactId,
        },
        types: [
          {
            associationCategory: 'HUBSPOT_DEFINED',
            associationTypeId: 204, // Contact to task association
          },
        ],
      },
    ],
  };
}

/**
 * Format duration in seconds to MM:SS
 */
function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

export default {
  createHubSpotNote,
  createHubSpotTask,
  createVideoNote,
  createVideoTask,
};
