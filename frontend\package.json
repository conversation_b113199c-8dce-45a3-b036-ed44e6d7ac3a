{"name": "echopilot-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.19.5", "@livekit/components-react": "^1.5.3", "axios": "^1.6.7", "clsx": "^2.1.0", "livekit-client": "^1.15.5", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.0.1", "tailwind-merge": "^2.2.1", "tus-js-client": "^3.1.1", "zustand": "^4.5.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}