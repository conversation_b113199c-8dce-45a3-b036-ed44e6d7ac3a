import fetch from 'node-fetch';

interface SlackMessage {
  text?: string;
  blocks?: any[];
  attachments?: any[];
  channel?: string;
  username?: string;
  icon_emoji?: string;
  icon_url?: string;
  thread_ts?: string;
}

/**
 * Send a message to a Slack webhook
 */
export async function sendSlackWebhook(
  webhookUrl: string,
  message: SlackMessage
): Promise<{ success: boolean; statusCode: number; responseTime: number }> {
  const startTime = Date.now();
  
  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });
    
    const responseTime = Date.now() - startTime;
    
    return {
      success: response.ok,
      statusCode: response.status,
      responseTime,
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    console.error(`Error sending Slack webhook to ${webhookUrl}:`, error);
    
    return {
      success: false,
      statusCode: 0,
      responseTime,
    };
  }
}

/**
 * Create a Slack message for a new video
 */
export function createVideoMessage(video: any): SlackMessage {
  const videoUrl = `${process.env.FRONTEND_URL || 'https://echopilot.example.com'}/v/${video.id}`;
  
  return {
    blocks: [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '🎬 New Video Available',
          emoji: true,
        },
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*${video.title || 'Untitled Video'}*\nUploaded by ${video.uploader.name}`,
        },
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*Duration:*\n${formatDuration(video.durationSec)}`,
          },
          {
            type: 'mrkdwn',
            text: `*Size:*\n${formatSize(video.sizeMb)}`,
          },
        ],
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: video.summary || 'No summary available yet.',
        },
      },
      {
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'View Video',
              emoji: true,
            },
            url: videoUrl,
            style: 'primary',
          },
        ],
      },
    ],
  };
}

/**
 * Format duration in seconds to MM:SS
 */
function formatDuration(seconds?: number): string {
  if (!seconds) return 'Unknown';
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * Format size in MB
 */
function formatSize(mb?: number): string {
  if (!mb) return 'Unknown';
  
  if (mb < 1) {
    return `${Math.round(mb * 1000)} KB`;
  }
  
  return `${mb.toFixed(1)} MB`;
}

export default {
  sendSlackWebhook,
  createVideoMessage,
};
